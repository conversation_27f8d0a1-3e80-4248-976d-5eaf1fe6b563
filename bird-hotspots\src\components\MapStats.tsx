import { BarChart3, Eye, MapPin } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

const MapStats = () => {
  const { species, observations } = useAppStore();

  const visibleSpecies = species.filter(s => s.isVisible);
  const visibleObservations = observations.filter(obs => 
    visibleSpecies.some(s => s.id === obs.speciesId)
  );

  const totalObservations = visibleObservations.length;
  const totalCount = visibleObservations.reduce((sum, obs) => sum + obs.count, 0);
  const avgCount = totalObservations > 0 ? Math.round(totalCount / totalObservations) : 0;

  return (
    <div className="absolute bottom-4 left-4 z-[1000] max-w-xs">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-3 sm:p-4">
        <div className="flex items-center mb-3">
          <BarChart3 className="h-4 w-4 text-blue-600 mr-2" />
          <h3 className="text-sm font-semibold text-gray-900">观察统计</h3>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Eye className="h-3 w-3 text-gray-500 mr-2" />
              <span className="text-gray-700">显示物种:</span>
            </div>
            <span className="font-medium text-gray-900">
              {visibleSpecies.length}/{species.length}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <MapPin className="h-3 w-3 text-gray-500 mr-2" />
              <span className="text-gray-700">观察点:</span>
            </div>
            <span className="font-medium text-gray-900">
              {totalObservations.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-700">总观察数:</span>
            <span className="font-medium text-gray-900">
              {totalCount.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-700">平均数量:</span>
            <span className="font-medium text-gray-900">
              {avgCount}
            </span>
          </div>
        </div>

        {/* Species breakdown */}
        {visibleSpecies.length > 0 && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-600 mb-2">物种分布:</div>
            <div className="space-y-1">
              {visibleSpecies.map(speciesItem => {
                const speciesObs = observations.filter(obs => obs.speciesId === speciesItem.id);
                const speciesCount = speciesObs.reduce((sum, obs) => sum + obs.count, 0);
                return (
                  <div key={speciesItem.id} className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: speciesItem.color }}
                      />
                      <span className="text-gray-700 truncate max-w-20">
                        {speciesItem.name}
                      </span>
                    </div>
                    <span className="text-gray-900 font-medium">
                      {speciesCount.toLocaleString()}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapStats;
