
import { useEffect } from 'react';
import Header from './components/Header';
import SearchFilters from './components/SearchFilters';
import MapContainer from './components/MapContainer';
import { useAppStore } from './store/useAppStore';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';

function App() {
  const initializeData = useAppStore((state) => state.initializeData);

  // Enable keyboard shortcuts
  useKeyboardShortcuts();

  useEffect(() => {
    initializeData();
  }, [initializeData]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="flex flex-col h-screen">
        <SearchFilters />
        <div className="flex-1 relative">
          <MapContainer />
        </div>
      </main>
    </div>
  );
}

export default App;
