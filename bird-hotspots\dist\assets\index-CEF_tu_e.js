(function(){const E=document.createElement("link").relList;if(E&&E.supports&&E.supports("modulepreload"))return;for(const N of document.querySelectorAll('link[rel="modulepreload"]'))p(N);new MutationObserver(N=>{for(const Y of N)if(Y.type==="childList")for(const G of Y.addedNodes)G.tagName==="LINK"&&G.rel==="modulepreload"&&p(G)}).observe(document,{childList:!0,subtree:!0});function x(N){const Y={};return N.integrity&&(Y.integrity=N.integrity),N.referrerPolicy&&(Y.referrerPolicy=N.referrerPolicy),N.crossOrigin==="use-credentials"?Y.credentials="include":N.crossOrigin==="anonymous"?Y.credentials="omit":Y.credentials="same-origin",Y}function p(N){if(N.ep)return;N.ep=!0;const Y=x(N);fetch(N.href,Y)}})();function Ed(g){return g&&g.__esModule&&Object.prototype.hasOwnProperty.call(g,"default")?g.default:g}var mc={exports:{}},Hs={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fd;function Vm(){if(fd)return Hs;fd=1;var g=Symbol.for("react.transitional.element"),E=Symbol.for("react.fragment");function x(p,N,Y){var G=null;if(Y!==void 0&&(G=""+Y),N.key!==void 0&&(G=""+N.key),"key"in N){Y={};for(var nt in N)nt!=="key"&&(Y[nt]=N[nt])}else Y=N;return N=Y.ref,{$$typeof:g,type:p,key:G,ref:N!==void 0?N:null,props:Y}}return Hs.Fragment=E,Hs.jsx=x,Hs.jsxs=x,Hs}var hd;function Xm(){return hd||(hd=1,mc.exports=Vm()),mc.exports}var b=Xm(),_c={exports:{}},ft={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dd;function Qm(){if(dd)return ft;dd=1;var g=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),Y=Symbol.for("react.consumer"),G=Symbol.for("react.context"),nt=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),B=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),tt=Symbol.iterator;function ct(m){return m===null||typeof m!="object"?null:(m=tt&&m[tt]||m["@@iterator"],typeof m=="function"?m:null)}var Pt={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ut=Object.assign,mt={};function Xt(m,R,V){this.props=m,this.context=R,this.refs=mt,this.updater=V||Pt}Xt.prototype.isReactComponent={},Xt.prototype.setState=function(m,R){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,R,"setState")},Xt.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function ne(){}ne.prototype=Xt.prototype;function Ae(m,R,V){this.props=m,this.context=R,this.refs=mt,this.updater=V||Pt}var Mt=Ae.prototype=new ne;Mt.constructor=Ae,Ut(Mt,Xt.prototype),Mt.isPureReactComponent=!0;var fe=Array.isArray,ht={H:null,A:null,T:null,S:null,V:null},he=Object.prototype.hasOwnProperty;function xe(m,R,V,k,J,I){return V=I.ref,{$$typeof:g,type:m,key:R,ref:V!==void 0?V:null,props:I}}function be(m,R){return xe(m.type,R,void 0,void 0,void 0,m.props)}function Ue(m){return typeof m=="object"&&m!==null&&m.$$typeof===g}function Hi(m){var R={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(V){return R[V]})}var jt=/\/+/g;function Et(m,R){return typeof m=="object"&&m!==null&&m.key!=null?Hi(""+m.key):R.toString(36)}function qi(){}function de(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(qi,qi):(m.status="pending",m.then(function(R){m.status==="pending"&&(m.status="fulfilled",m.value=R)},function(R){m.status==="pending"&&(m.status="rejected",m.reason=R)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function ae(m,R,V,k,J){var I=typeof m;(I==="undefined"||I==="boolean")&&(m=null);var X=!1;if(m===null)X=!0;else switch(I){case"bigint":case"string":case"number":X=!0;break;case"object":switch(m.$$typeof){case g:case E:X=!0;break;case q:return X=m._init,ae(X(m._payload),R,V,k,J)}}if(X)return J=J(m),X=k===""?"."+Et(m,0):k,fe(J)?(V="",X!=null&&(V=X.replace(jt,"$&/")+"/"),ae(J,R,V,"",function(mi){return mi})):J!=null&&(Ue(J)&&(J=be(J,V+(J.key==null||m&&m.key===J.key?"":(""+J.key).replace(jt,"$&/")+"/")+X)),R.push(J)),1;X=0;var kt=k===""?".":k+":";if(fe(m))for(var bt=0;bt<m.length;bt++)k=m[bt],I=kt+Et(k,bt),X+=ae(k,R,V,I,J);else if(bt=ct(m),typeof bt=="function")for(m=bt.call(m),bt=0;!(k=m.next()).done;)k=k.value,I=kt+Et(k,bt++),X+=ae(k,R,V,I,J);else if(I==="object"){if(typeof m.then=="function")return ae(de(m),R,V,k,J);throw R=String(m),Error("Objects are not valid as a React child (found: "+(R==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":R)+"). If you meant to render a collection of children, use an array instead.")}return X}function D(m,R,V){if(m==null)return m;var k=[],J=0;return ae(m,k,"","",function(I){return R.call(V,I,J++)}),k}function Q(m){if(m._status===-1){var R=m._result;R=R(),R.then(function(V){(m._status===0||m._status===-1)&&(m._status=1,m._result=V)},function(V){(m._status===0||m._status===-1)&&(m._status=2,m._result=V)}),m._status===-1&&(m._status=0,m._result=R)}if(m._status===1)return m._result.default;throw m._result}var P=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var R=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(R))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function wt(){}return ft.Children={map:D,forEach:function(m,R,V){D(m,function(){R.apply(this,arguments)},V)},count:function(m){var R=0;return D(m,function(){R++}),R},toArray:function(m){return D(m,function(R){return R})||[]},only:function(m){if(!Ue(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},ft.Component=Xt,ft.Fragment=x,ft.Profiler=N,ft.PureComponent=Ae,ft.StrictMode=p,ft.Suspense=C,ft.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ht,ft.__COMPILER_RUNTIME={__proto__:null,c:function(m){return ht.H.useMemoCache(m)}},ft.cache=function(m){return function(){return m.apply(null,arguments)}},ft.cloneElement=function(m,R,V){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var k=Ut({},m.props),J=m.key,I=void 0;if(R!=null)for(X in R.ref!==void 0&&(I=void 0),R.key!==void 0&&(J=""+R.key),R)!he.call(R,X)||X==="key"||X==="__self"||X==="__source"||X==="ref"&&R.ref===void 0||(k[X]=R[X]);var X=arguments.length-2;if(X===1)k.children=V;else if(1<X){for(var kt=Array(X),bt=0;bt<X;bt++)kt[bt]=arguments[bt+2];k.children=kt}return xe(m.type,J,void 0,void 0,I,k)},ft.createContext=function(m){return m={$$typeof:G,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:Y,_context:m},m},ft.createElement=function(m,R,V){var k,J={},I=null;if(R!=null)for(k in R.key!==void 0&&(I=""+R.key),R)he.call(R,k)&&k!=="key"&&k!=="__self"&&k!=="__source"&&(J[k]=R[k]);var X=arguments.length-2;if(X===1)J.children=V;else if(1<X){for(var kt=Array(X),bt=0;bt<X;bt++)kt[bt]=arguments[bt+2];J.children=kt}if(m&&m.defaultProps)for(k in X=m.defaultProps,X)J[k]===void 0&&(J[k]=X[k]);return xe(m,I,void 0,void 0,null,J)},ft.createRef=function(){return{current:null}},ft.forwardRef=function(m){return{$$typeof:nt,render:m}},ft.isValidElement=Ue,ft.lazy=function(m){return{$$typeof:q,_payload:{_status:-1,_result:m},_init:Q}},ft.memo=function(m,R){return{$$typeof:B,type:m,compare:R===void 0?null:R}},ft.startTransition=function(m){var R=ht.T,V={};ht.T=V;try{var k=m(),J=ht.S;J!==null&&J(V,k),typeof k=="object"&&k!==null&&typeof k.then=="function"&&k.then(wt,P)}catch(I){P(I)}finally{ht.T=R}},ft.unstable_useCacheRefresh=function(){return ht.H.useCacheRefresh()},ft.use=function(m){return ht.H.use(m)},ft.useActionState=function(m,R,V){return ht.H.useActionState(m,R,V)},ft.useCallback=function(m,R){return ht.H.useCallback(m,R)},ft.useContext=function(m){return ht.H.useContext(m)},ft.useDebugValue=function(){},ft.useDeferredValue=function(m,R){return ht.H.useDeferredValue(m,R)},ft.useEffect=function(m,R,V){var k=ht.H;if(typeof V=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return k.useEffect(m,R)},ft.useId=function(){return ht.H.useId()},ft.useImperativeHandle=function(m,R,V){return ht.H.useImperativeHandle(m,R,V)},ft.useInsertionEffect=function(m,R){return ht.H.useInsertionEffect(m,R)},ft.useLayoutEffect=function(m,R){return ht.H.useLayoutEffect(m,R)},ft.useMemo=function(m,R){return ht.H.useMemo(m,R)},ft.useOptimistic=function(m,R){return ht.H.useOptimistic(m,R)},ft.useReducer=function(m,R,V){return ht.H.useReducer(m,R,V)},ft.useRef=function(m){return ht.H.useRef(m)},ft.useState=function(m){return ht.H.useState(m)},ft.useSyncExternalStore=function(m,R,V){return ht.H.useSyncExternalStore(m,R,V)},ft.useTransition=function(){return ht.H.useTransition()},ft.version="19.1.0",ft}var md;function Sc(){return md||(md=1,_c.exports=Qm()),_c.exports}var ie=Sc();const _d=Ed(ie);var vc={exports:{}},qs={},pc={exports:{}},gc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vd;function Km(){return vd||(vd=1,function(g){function E(D,Q){var P=D.length;D.push(Q);t:for(;0<P;){var wt=P-1>>>1,m=D[wt];if(0<N(m,Q))D[wt]=Q,D[P]=m,P=wt;else break t}}function x(D){return D.length===0?null:D[0]}function p(D){if(D.length===0)return null;var Q=D[0],P=D.pop();if(P!==Q){D[0]=P;t:for(var wt=0,m=D.length,R=m>>>1;wt<R;){var V=2*(wt+1)-1,k=D[V],J=V+1,I=D[J];if(0>N(k,P))J<m&&0>N(I,k)?(D[wt]=I,D[J]=P,wt=J):(D[wt]=k,D[V]=P,wt=V);else if(J<m&&0>N(I,P))D[wt]=I,D[J]=P,wt=J;else break t}}return Q}function N(D,Q){var P=D.sortIndex-Q.sortIndex;return P!==0?P:D.id-Q.id}if(g.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var Y=performance;g.unstable_now=function(){return Y.now()}}else{var G=Date,nt=G.now();g.unstable_now=function(){return G.now()-nt}}var C=[],B=[],q=1,tt=null,ct=3,Pt=!1,Ut=!1,mt=!1,Xt=!1,ne=typeof setTimeout=="function"?setTimeout:null,Ae=typeof clearTimeout=="function"?clearTimeout:null,Mt=typeof setImmediate<"u"?setImmediate:null;function fe(D){for(var Q=x(B);Q!==null;){if(Q.callback===null)p(B);else if(Q.startTime<=D)p(B),Q.sortIndex=Q.expirationTime,E(C,Q);else break;Q=x(B)}}function ht(D){if(mt=!1,fe(D),!Ut)if(x(C)!==null)Ut=!0,he||(he=!0,Et());else{var Q=x(B);Q!==null&&ae(ht,Q.startTime-D)}}var he=!1,xe=-1,be=5,Ue=-1;function Hi(){return Xt?!0:!(g.unstable_now()-Ue<be)}function jt(){if(Xt=!1,he){var D=g.unstable_now();Ue=D;var Q=!0;try{t:{Ut=!1,mt&&(mt=!1,Ae(xe),xe=-1),Pt=!0;var P=ct;try{e:{for(fe(D),tt=x(C);tt!==null&&!(tt.expirationTime>D&&Hi());){var wt=tt.callback;if(typeof wt=="function"){tt.callback=null,ct=tt.priorityLevel;var m=wt(tt.expirationTime<=D);if(D=g.unstable_now(),typeof m=="function"){tt.callback=m,fe(D),Q=!0;break e}tt===x(C)&&p(C),fe(D)}else p(C);tt=x(C)}if(tt!==null)Q=!0;else{var R=x(B);R!==null&&ae(ht,R.startTime-D),Q=!1}}break t}finally{tt=null,ct=P,Pt=!1}Q=void 0}}finally{Q?Et():he=!1}}}var Et;if(typeof Mt=="function")Et=function(){Mt(jt)};else if(typeof MessageChannel<"u"){var qi=new MessageChannel,de=qi.port2;qi.port1.onmessage=jt,Et=function(){de.postMessage(null)}}else Et=function(){ne(jt,0)};function ae(D,Q){xe=ne(function(){D(g.unstable_now())},Q)}g.unstable_IdlePriority=5,g.unstable_ImmediatePriority=1,g.unstable_LowPriority=4,g.unstable_NormalPriority=3,g.unstable_Profiling=null,g.unstable_UserBlockingPriority=2,g.unstable_cancelCallback=function(D){D.callback=null},g.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):be=0<D?Math.floor(1e3/D):5},g.unstable_getCurrentPriorityLevel=function(){return ct},g.unstable_next=function(D){switch(ct){case 1:case 2:case 3:var Q=3;break;default:Q=ct}var P=ct;ct=Q;try{return D()}finally{ct=P}},g.unstable_requestPaint=function(){Xt=!0},g.unstable_runWithPriority=function(D,Q){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var P=ct;ct=D;try{return Q()}finally{ct=P}},g.unstable_scheduleCallback=function(D,Q,P){var wt=g.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?wt+P:wt):P=wt,D){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=P+m,D={id:q++,callback:Q,priorityLevel:D,startTime:P,expirationTime:m,sortIndex:-1},P>wt?(D.sortIndex=P,E(B,D),x(C)===null&&D===x(B)&&(mt?(Ae(xe),xe=-1):mt=!0,ae(ht,P-wt))):(D.sortIndex=m,E(C,D),Ut||Pt||(Ut=!0,he||(he=!0,Et()))),D},g.unstable_shouldYield=Hi,g.unstable_wrapCallback=function(D){var Q=ct;return function(){var P=ct;ct=Q;try{return D.apply(this,arguments)}finally{ct=P}}}}(gc)),gc}var pd;function Jm(){return pd||(pd=1,pc.exports=Km()),pc.exports}var yc={exports:{}},ze={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gd;function Wm(){if(gd)return ze;gd=1;var g=Sc();function E(C){var B="https://react.dev/errors/"+C;if(1<arguments.length){B+="?args[]="+encodeURIComponent(arguments[1]);for(var q=2;q<arguments.length;q++)B+="&args[]="+encodeURIComponent(arguments[q])}return"Minified React error #"+C+"; visit "+B+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function x(){}var p={d:{f:x,r:function(){throw Error(E(522))},D:x,C:x,L:x,m:x,X:x,S:x,M:x},p:0,findDOMNode:null},N=Symbol.for("react.portal");function Y(C,B,q){var tt=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:N,key:tt==null?null:""+tt,children:C,containerInfo:B,implementation:q}}var G=g.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function nt(C,B){if(C==="font")return"";if(typeof B=="string")return B==="use-credentials"?B:""}return ze.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=p,ze.createPortal=function(C,B){var q=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!B||B.nodeType!==1&&B.nodeType!==9&&B.nodeType!==11)throw Error(E(299));return Y(C,B,null,q)},ze.flushSync=function(C){var B=G.T,q=p.p;try{if(G.T=null,p.p=2,C)return C()}finally{G.T=B,p.p=q,p.d.f()}},ze.preconnect=function(C,B){typeof C=="string"&&(B?(B=B.crossOrigin,B=typeof B=="string"?B==="use-credentials"?B:"":void 0):B=null,p.d.C(C,B))},ze.prefetchDNS=function(C){typeof C=="string"&&p.d.D(C)},ze.preinit=function(C,B){if(typeof C=="string"&&B&&typeof B.as=="string"){var q=B.as,tt=nt(q,B.crossOrigin),ct=typeof B.integrity=="string"?B.integrity:void 0,Pt=typeof B.fetchPriority=="string"?B.fetchPriority:void 0;q==="style"?p.d.S(C,typeof B.precedence=="string"?B.precedence:void 0,{crossOrigin:tt,integrity:ct,fetchPriority:Pt}):q==="script"&&p.d.X(C,{crossOrigin:tt,integrity:ct,fetchPriority:Pt,nonce:typeof B.nonce=="string"?B.nonce:void 0})}},ze.preinitModule=function(C,B){if(typeof C=="string")if(typeof B=="object"&&B!==null){if(B.as==null||B.as==="script"){var q=nt(B.as,B.crossOrigin);p.d.M(C,{crossOrigin:q,integrity:typeof B.integrity=="string"?B.integrity:void 0,nonce:typeof B.nonce=="string"?B.nonce:void 0})}}else B==null&&p.d.M(C)},ze.preload=function(C,B){if(typeof C=="string"&&typeof B=="object"&&B!==null&&typeof B.as=="string"){var q=B.as,tt=nt(q,B.crossOrigin);p.d.L(C,q,{crossOrigin:tt,integrity:typeof B.integrity=="string"?B.integrity:void 0,nonce:typeof B.nonce=="string"?B.nonce:void 0,type:typeof B.type=="string"?B.type:void 0,fetchPriority:typeof B.fetchPriority=="string"?B.fetchPriority:void 0,referrerPolicy:typeof B.referrerPolicy=="string"?B.referrerPolicy:void 0,imageSrcSet:typeof B.imageSrcSet=="string"?B.imageSrcSet:void 0,imageSizes:typeof B.imageSizes=="string"?B.imageSizes:void 0,media:typeof B.media=="string"?B.media:void 0})}},ze.preloadModule=function(C,B){if(typeof C=="string")if(B){var q=nt(B.as,B.crossOrigin);p.d.m(C,{as:typeof B.as=="string"&&B.as!=="script"?B.as:void 0,crossOrigin:q,integrity:typeof B.integrity=="string"?B.integrity:void 0})}else p.d.m(C)},ze.requestFormReset=function(C){p.d.r(C)},ze.unstable_batchedUpdates=function(C,B){return C(B)},ze.useFormState=function(C,B,q){return G.H.useFormState(C,B,q)},ze.useFormStatus=function(){return G.H.useHostTransitionStatus()},ze.version="19.1.0",ze}var yd;function Fm(){if(yd)return yc.exports;yd=1;function g(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(g)}catch(E){console.error(E)}}return g(),yc.exports=Wm(),yc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xd;function Im(){if(xd)return qs;xd=1;var g=Jm(),E=Sc(),x=Fm();function p(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function N(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Y(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function G(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function nt(t){if(Y(t)!==t)throw Error(p(188))}function C(t){var e=t.alternate;if(!e){if(e=Y(t),e===null)throw Error(p(188));return e!==t?null:t}for(var n=t,l=e;;){var o=n.return;if(o===null)break;var u=o.alternate;if(u===null){if(l=o.return,l!==null){n=l;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===n)return nt(o),t;if(u===l)return nt(o),e;u=u.sibling}throw Error(p(188))}if(n.return!==l.return)n=o,l=u;else{for(var f=!1,d=o.child;d;){if(d===n){f=!0,n=o,l=u;break}if(d===l){f=!0,l=o,n=u;break}d=d.sibling}if(!f){for(d=u.child;d;){if(d===n){f=!0,n=u,l=o;break}if(d===l){f=!0,l=u,n=o;break}d=d.sibling}if(!f)throw Error(p(189))}}if(n.alternate!==l)throw Error(p(190))}if(n.tag!==3)throw Error(p(188));return n.stateNode.current===n?t:e}function B(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=B(t),e!==null)return e;t=t.sibling}return null}var q=Object.assign,tt=Symbol.for("react.element"),ct=Symbol.for("react.transitional.element"),Pt=Symbol.for("react.portal"),Ut=Symbol.for("react.fragment"),mt=Symbol.for("react.strict_mode"),Xt=Symbol.for("react.profiler"),ne=Symbol.for("react.provider"),Ae=Symbol.for("react.consumer"),Mt=Symbol.for("react.context"),fe=Symbol.for("react.forward_ref"),ht=Symbol.for("react.suspense"),he=Symbol.for("react.suspense_list"),xe=Symbol.for("react.memo"),be=Symbol.for("react.lazy"),Ue=Symbol.for("react.activity"),Hi=Symbol.for("react.memo_cache_sentinel"),jt=Symbol.iterator;function Et(t){return t===null||typeof t!="object"?null:(t=jt&&t[jt]||t["@@iterator"],typeof t=="function"?t:null)}var qi=Symbol.for("react.client.reference");function de(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===qi?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Ut:return"Fragment";case Xt:return"Profiler";case mt:return"StrictMode";case ht:return"Suspense";case he:return"SuspenseList";case Ue:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Pt:return"Portal";case Mt:return(t.displayName||"Context")+".Provider";case Ae:return(t._context.displayName||"Context")+".Consumer";case fe:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case xe:return e=t.displayName||null,e!==null?e:de(t.type)||"Memo";case be:e=t._payload,t=t._init;try{return de(t(e))}catch{}}return null}var ae=Array.isArray,D=E.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=x.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},wt=[],m=-1;function R(t){return{current:t}}function V(t){0>m||(t.current=wt[m],wt[m]=null,m--)}function k(t,e){m++,wt[m]=t.current,t.current=e}var J=R(null),I=R(null),X=R(null),kt=R(null);function bt(t,e){switch(k(X,e),k(I,t),k(J,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Hh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Hh(e),t=qh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}V(J),k(J,t)}function mi(){V(J),V(I),V(X)}function ma(t){t.memoizedState!==null&&k(kt,t);var e=J.current,n=qh(e,t.type);e!==n&&(k(I,t),k(J,n))}function yn(t){I.current===t&&(V(J),V(I)),kt.current===t&&(V(kt),Rs._currentValue=P)}var Pi=Object.prototype.hasOwnProperty,_a=g.unstable_scheduleCallback,Bl=g.unstable_cancelCallback,ks=g.unstable_shouldYield,Gs=g.unstable_requestPaint,je=g.unstable_now,Va=g.unstable_getCurrentPriorityLevel,Ys=g.unstable_ImmediatePriority,Zl=g.unstable_UserBlockingPriority,xn=g.unstable_NormalPriority,Vs=g.unstable_LowPriority,Ul=g.unstable_IdlePriority,yu=g.log,xu=g.unstable_setDisableYieldValue,ki=null,Se=null;function _i(t){if(typeof yu=="function"&&xu(t),Se&&typeof Se.setStrictMode=="function")try{Se.setStrictMode(ki,t)}catch{}}var Oe=Math.clz32?Math.clz32:bu,Xs=Math.log,Qs=Math.LN2;function bu(t){return t>>>=0,t===0?32:31-(Xs(t)/Qs|0)|0}var va=256,bn=4194304;function wi(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Xa(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var o=0,u=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var d=l&134217727;return d!==0?(l=d&~u,l!==0?o=wi(l):(f&=d,f!==0?o=wi(f):n||(n=d&~t,n!==0&&(o=wi(n))))):(d=l&~u,d!==0?o=wi(d):f!==0?o=wi(f):n||(n=l&~t,n!==0&&(o=wi(n)))),o===0?0:e!==0&&e!==o&&(e&u)===0&&(u=o&-o,n=e&-e,u>=n||u===32&&(n&4194048)!==0)?e:o}function vi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Su(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ks(){var t=va;return va<<=1,(va&4194048)===0&&(va=256),t}function jl(){var t=bn;return bn<<=1,(bn&62914560)===0&&(bn=4194304),t}function Qa(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Sn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Tu(t,e,n,l,o,u){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var d=t.entanglements,v=t.expirationTimes,w=t.hiddenUpdates;for(n=f&~n;0<n;){var Z=31-Oe(n),j=1<<Z;d[Z]=0,v[Z]=-1;var z=w[Z];if(z!==null)for(w[Z]=null,Z=0;Z<z.length;Z++){var A=z[Z];A!==null&&(A.lane&=-536870913)}n&=~j}l!==0&&Js(t,l,0),u!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=u&~(f&~e))}function Js(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-Oe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function Ws(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-Oe(n),o=1<<l;o&e|t[l]&e&&(t[l]|=e),n&=~o}}function Hl(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ql(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Fs(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:ld(t.type))}function Pl(t,e){var n=Q.p;try{return Q.p=t,e()}finally{Q.p=n}}var Ei=Math.random().toString(36).slice(2),le="__reactFiber$"+Ei,Te="__reactProps$"+Ei,Tn="__reactContainer$"+Ei,He="__reactEvents$"+Ei,et="__reactListeners$"+Ei,Is="__reactHandles$"+Ei,kl="__reactResources$"+Ei,Mn="__reactMarker$"+Ei;function Ka(t){delete t[le],delete t[Te],delete t[He],delete t[et],delete t[Is]}function zi(t){var e=t[le];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Tn]||n[le]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Yh(t);t!==null;){if(n=t[le])return n;t=Yh(t)}return e}t=n,n=t.parentNode}return null}function Gi(t){if(t=t[le]||t[Tn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ni(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(p(33))}function Yi(t){var e=t[kl];return e||(e=t[kl]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ft(t){t[Mn]=!0}var $s=new Set,to={};function Vi(t,e){Xi(t,e),Xi(t+"Capture",e)}function Xi(t,e){for(to[t]=e,t=0;t<e.length;t++)$s.add(e[t])}var Mu=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),pa={},eo={};function wu(t){return Pi.call(eo,t)?!0:Pi.call(pa,t)?!1:Mu.test(t)?eo[t]=!0:(pa[t]=!0,!1)}function Ja(t,e,n){if(wu(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Wa(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function pi(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var ga,wn;function Qi(t){if(ga===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ga=e&&e[1]||"",wn=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ga+t+wn}var Fa=!1;function Ki(t,e){if(!t||Fa)return"";Fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var j=function(){throw Error()};if(Object.defineProperty(j.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(j,[])}catch(A){var z=A}Reflect.construct(t,[],j)}else{try{j.call()}catch(A){z=A}t.call(j.prototype)}}else{try{throw Error()}catch(A){z=A}(j=t())&&typeof j.catch=="function"&&j.catch(function(){})}}catch(A){if(A&&z&&typeof A.stack=="string")return[A.stack,z.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),f=u[0],d=u[1];if(f&&d){var v=f.split(`
`),w=d.split(`
`);for(o=l=0;l<v.length&&!v[l].includes("DetermineComponentFrameRoot");)l++;for(;o<w.length&&!w[o].includes("DetermineComponentFrameRoot");)o++;if(l===v.length||o===w.length)for(l=v.length-1,o=w.length-1;1<=l&&0<=o&&v[l]!==w[o];)o--;for(;1<=l&&0<=o;l--,o--)if(v[l]!==w[o]){if(l!==1||o!==1)do if(l--,o--,0>o||v[l]!==w[o]){var Z=`
`+v[l].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=l&&0<=o);break}}}finally{Fa=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Qi(n):""}function yt(t){switch(t.tag){case 26:case 27:case 5:return Qi(t.type);case 16:return Qi("Lazy");case 13:return Qi("Suspense");case 19:return Qi("SuspenseList");case 0:case 15:return Ki(t.type,!1);case 11:return Ki(t.type.render,!1);case 1:return Ki(t.type,!0);case 31:return Qi("Activity");default:return""}}function Dt(t){try{var e="";do e+=yt(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ji(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function En(t){var e=Ji(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(f){l=""+f,u.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zn(t){t._valueTracker||(t._valueTracker=En(t))}function rt(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Ji(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function Rt(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Gl=/[\n"\\]/g;function _e(t){return t.replace(Gl,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Me(t,e,n,l,o,u,f,d){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+me(e)):t.value!==""+me(e)&&(t.value=""+me(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?Ln(t,f,me(e)):n!=null?Ln(t,f,me(n)):l!=null&&t.removeAttribute("value"),o==null&&u!=null&&(t.defaultChecked=!!u),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+me(d):t.removeAttribute("name")}function io(t,e,n,l,o,u,f,d){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;n=n!=null?""+me(n):"",e=e!=null?""+me(e):n,d||e===t.value||(t.value=e),t.defaultValue=e}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=d?t.checked:!!l,t.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function Ln(t,e,n){e==="number"&&Rt(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function qe(t,e,n,l){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&l&&(t[n].defaultSelected=!0)}else{for(n=""+me(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,l&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Gt(t,e,n){if(e!=null&&(e=""+me(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+me(n):""}function Li(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(p(92));if(ae(l)){if(1<l.length)throw Error(p(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=me(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function ai(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var ya=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ia(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||ya.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function An(t,e,n){if(e!=null&&typeof e!="object")throw Error(p(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var o in e)l=e[o],e.hasOwnProperty(o)&&n[o]!==l&&Ia(t,o,l)}else for(var u in e)e.hasOwnProperty(u)&&Ia(t,u,e[u])}function xa(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Yl=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$a=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function On(t){return $a.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var ba=null;function Nn(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Wi=null,Ai=null;function no(t){var e=Gi(t);if(e&&(t=e.stateNode)){var n=t[Te]||null;t:switch(t=e.stateNode,e.type){case"input":if(Me(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+_e(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var o=l[Te]||null;if(!o)throw Error(p(90));Me(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&rt(l)}break t;case"textarea":Gt(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&qe(t,!!n.multiple,e,!1)}}}var ut=!1;function Ve(t,e,n){if(ut)return t(e,n);ut=!0;try{var l=t(e);return l}finally{if(ut=!1,(Wi!==null||Ai!==null)&&(Io(),Wi&&(e=Wi,t=Ai,Ai=Wi=null,no(e),t)))for(e=0;e<t.length;e++)no(t[e])}}function St(t,e){var n=t.stateNode;if(n===null)return null;var l=n[Te]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(p(231,e,typeof n));return n}var li=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sa=!1;if(li)try{var Fi={};Object.defineProperty(Fi,"passive",{get:function(){Sa=!0}}),window.addEventListener("test",Fi,Fi),window.removeEventListener("test",Fi,Fi)}catch{Sa=!1}var si=null,gi=null,Cn=null;function Dn(){if(Cn)return Cn;var t,e=gi,n=e.length,l,o="value"in si?si.value:si.textContent,u=o.length;for(t=0;t<n&&e[t]===o[t];t++);var f=n-t;for(l=1;l<=f&&e[n-l]===o[u-l];l++);return Cn=o.slice(t,1<l?1-l:void 0)}function Qt(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function oi(){return!0}function Vl(){return!1}function ve(t){function e(n,l,o,u,f){this._reactName=n,this._targetInst=o,this.type=l,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(n=t[d],this[d]=n?n(u):u[d]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?oi:Vl,this.isPropagationStopped=Vl,this}return q(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=oi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=oi)},persist:function(){},isPersistent:oi}),e}var Ii={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ta=ve(Ii),$i=q({},Ii,{view:0,detail:0}),Eu=ve($i),tl,_t,Ma,we=q({},$i,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:el,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ma&&(Ma&&t.type==="mousemove"?(tl=t.screenX-Ma.screenX,_t=t.screenY-Ma.screenY):_t=tl=0,Ma=t),tl)},movementY:function(t){return"movementY"in t?t.movementY:_t}}),Rn=ve(we),ao=q({},we,{dataTransfer:0}),zu=ve(ao),Xl=q({},$i,{relatedTarget:0}),Ql=ve(Xl),lo=q({},Ii,{animationName:0,elapsedTime:0,pseudoElement:0}),Lu=ve(lo),Au=q({},Ii,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Kl=ve(Au),Ou=q({},Ii,{data:0}),Xe=ve(Ou),Nu={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},so={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Oi={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function oo(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Oi[t])?!!e[t]:!1}function el(){return oo}var Jl=q({},$i,{key:function(t){if(t.key){var e=Nu[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Qt(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?so[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:el,charCode:function(t){return t.type==="keypress"?Qt(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Qt(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Cu=ve(Jl),uo=q({},we,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wl=ve(uo),Du=q({},$i,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:el}),Ru=ve(Du),Fl=q({},Ii,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bu=ve(Fl),ro=q({},we,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),co=ve(ro),il=q({},Ii,{newState:0,oldState:0}),tn=ve(il),Zu=[9,13,27,32],en=li&&"CompositionEvent"in window,se=null;li&&"documentMode"in document&&(se=document.documentMode);var fo=li&&"TextEvent"in window&&!se,Il=li&&(!en||se&&8<se&&11>=se),ho=" ",nl=!1;function al(t,e){switch(t){case"keyup":return Zu.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function mo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Bn=!1;function _o(t,e){switch(t){case"compositionend":return mo(e);case"keypress":return e.which!==32?null:(nl=!0,ho);case"textInput":return t=e.data,t===ho&&nl?null:t;default:return null}}function Uu(t,e){if(Bn)return t==="compositionend"||!en&&al(t,e)?(t=Dn(),Cn=gi=si=null,Bn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Il&&e.locale!=="ko"?null:e.data;default:return null}}var Qe={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function nn(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Qe[t.type]:e==="textarea"}function vo(t,e,n,l){Wi?Ai?Ai.push(l):Ai=[l]:Wi=l,e=au(e,"onChange"),0<e.length&&(n=new Ta("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Ne=null,wa=null;function Zn(t){Rh(t,0)}function ll(t){var e=ni(t);if(rt(e))return t}function Un(t,e){if(t==="change")return e}var $l=!1;if(li){var jn;if(li){var ts="oninput"in document;if(!ts){var yi=document.createElement("div");yi.setAttribute("oninput","return;"),ts=typeof yi.oninput=="function"}jn=ts}else jn=!1;$l=jn&&(!document.documentMode||9<document.documentMode)}function Ea(){Ne&&(Ne.detachEvent("onpropertychange",po),wa=Ne=null)}function po(t){if(t.propertyName==="value"&&ll(wa)){var e=[];vo(e,wa,t,Nn(t)),Ve(Zn,e)}}function es(t,e,n){t==="focusin"?(Ea(),Ne=e,wa=n,Ne.attachEvent("onpropertychange",po)):t==="focusout"&&Ea()}function ju(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ll(wa)}function xi(t,e){if(t==="click")return ll(e)}function Hu(t,e){if(t==="input"||t==="change")return ll(e)}function Hn(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ce=typeof Object.is=="function"?Object.is:Hn;function De(t,e){if(Ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var o=n[l];if(!Pi.call(e,o)||!Ce(t[o],e[o]))return!1}return!0}function za(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function is(t,e){var n=za(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=za(n)}}function sl(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?sl(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function La(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Rt(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Rt(t.document)}return e}function Aa(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ol=li&&"documentMode"in document&&11>=document.documentMode,Ke=null,qn=null,an=null,ul=!1;function go(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ul||Ke==null||Ke!==Rt(l)||(l=Ke,"selectionStart"in l&&Aa(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),an&&De(an,l)||(an=l,l=au(qn,"onSelect"),0<l.length&&(e=new Ta("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Ke)))}function ui(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Pn={animationend:ui("Animation","AnimationEnd"),animationiteration:ui("Animation","AnimationIteration"),animationstart:ui("Animation","AnimationStart"),transitionrun:ui("Transition","TransitionRun"),transitionstart:ui("Transition","TransitionStart"),transitioncancel:ui("Transition","TransitionCancel"),transitionend:ui("Transition","TransitionEnd")},rl={},yo={};li&&(yo=document.createElement("div").style,"AnimationEvent"in window||(delete Pn.animationend.animation,delete Pn.animationiteration.animation,delete Pn.animationstart.animation),"TransitionEvent"in window||delete Pn.transitionend.transition);function Ni(t){if(rl[t])return rl[t];if(!Pn[t])return t;var e=Pn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in yo)return rl[t]=e[n];return t}var xo=Ni("animationend"),Je=Ni("animationiteration"),Oa=Ni("animationstart"),qu=Ni("transitionrun"),cl=Ni("transitionstart"),Pu=Ni("transitioncancel"),ns=Ni("transitionend"),bo=new Map,ln="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ln.push("scrollEnd");function We(t,e){bo.set(t,e),Vi(e,[t])}var sn=new WeakMap;function Re(t,e){if(typeof t=="object"&&t!==null){var n=sn.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Dt(e)},sn.set(t,e),e)}return{value:t,source:e,stack:Dt(e)}}var Be=[],kn=0,Fe=0;function Na(){for(var t=kn,e=Fe=kn=0;e<t;){var n=Be[e];Be[e++]=null;var l=Be[e];Be[e++]=null;var o=Be[e];Be[e++]=null;var u=Be[e];if(Be[e++]=null,l!==null&&o!==null){var f=l.pending;f===null?o.next=o:(o.next=f.next,f.next=o),l.pending=o}u!==0&&Da(n,o,u)}}function Ca(t,e,n,l){Be[kn++]=t,Be[kn++]=e,Be[kn++]=n,Be[kn++]=l,Fe|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function on(t,e,n,l){return Ca(t,e,n,l),Ci(t)}function Gn(t,e){return Ca(t,null,null,e),Ci(t)}function Da(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var o=!1,u=t.return;u!==null;)u.childLanes|=n,l=u.alternate,l!==null&&(l.childLanes|=n),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(o=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,o&&e!==null&&(o=31-Oe(n),t=u.hiddenUpdates,l=t[o],l===null?t[o]=[e]:l.push(e),e.lane=n|536870912),u):null}function Ci(t){if(50<Es)throw Es=0,jr=null,Error(p(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var un={};function So(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ze(t,e,n,l){return new So(t,e,n,l)}function fl(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ri(t,e){var n=t.alternate;return n===null?(n=Ze(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function as(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ra(t,e,n,l,o,u){var f=0;if(l=t,typeof t=="function")fl(t)&&(f=1);else if(typeof t=="string")f=Cm(t,n,J.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Ue:return t=Ze(31,n,e,o),t.elementType=Ue,t.lanes=u,t;case Ut:return Di(n.children,o,u,e);case mt:f=8,o|=24;break;case Xt:return t=Ze(12,n,e,o|2),t.elementType=Xt,t.lanes=u,t;case ht:return t=Ze(13,n,e,o),t.elementType=ht,t.lanes=u,t;case he:return t=Ze(19,n,e,o),t.elementType=he,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ne:case Mt:f=10;break t;case Ae:f=9;break t;case fe:f=11;break t;case xe:f=14;break t;case be:f=16,l=null;break t}f=29,n=Error(p(130,t===null?"null":typeof t,"")),l=null}return e=Ze(f,n,e,o),e.elementType=t,e.type=l,e.lanes=u,e}function Di(t,e,n,l){return t=Ze(7,t,l,e),t.lanes=n,t}function ls(t,e,n){return t=Ze(6,t,null,e),t.lanes=n,t}function hl(t,e,n){return e=Ze(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var rn=[],Yn=0,i=null,a=0,s=[],r=0,c=null,h=1,_="";function T(t,e){rn[Yn++]=a,rn[Yn++]=i,i=t,a=e}function O(t,e,n){s[r++]=h,s[r++]=_,s[r++]=c,c=t;var l=h;t=_;var o=32-Oe(l)-1;l&=~(1<<o),n+=1;var u=32-Oe(e)+o;if(30<u){var f=o-o%5;u=(l&(1<<f)-1).toString(32),l>>=f,o-=f,h=1<<32-Oe(e)+o|n<<o|l,_=u+t}else h=1<<u|n<<o|l,_=t}function H(t){t.return!==null&&(T(t,1),O(t,1,0))}function K(t){for(;t===i;)i=rn[--Yn],rn[Yn]=null,a=rn[--Yn],rn[Yn]=null;for(;t===c;)c=s[--r],s[r]=null,_=s[--r],s[r]=null,h=s[--r],s[r]=null}var W=null,$=null,ot=!1,Bt=null,Yt=!1,oe=Error(p(519));function Pe(t){var e=Error(p(418,""));throw Xn(Re(e,t)),oe}function To(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[le]=t,e[Te]=l,n){case"dialog":gt("cancel",e),gt("close",e);break;case"iframe":case"object":case"embed":gt("load",e);break;case"video":case"audio":for(n=0;n<Ls.length;n++)gt(Ls[n],e);break;case"source":gt("error",e);break;case"img":case"image":case"link":gt("error",e),gt("load",e);break;case"details":gt("toggle",e);break;case"input":gt("invalid",e),io(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),zn(e);break;case"select":gt("invalid",e);break;case"textarea":gt("invalid",e),Li(e,l.value,l.defaultValue,l.children),zn(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||jh(e.textContent,n)?(l.popover!=null&&(gt("beforetoggle",e),gt("toggle",e)),l.onScroll!=null&&gt("scroll",e),l.onScrollEnd!=null&&gt("scrollend",e),l.onClick!=null&&(e.onclick=lu),e=!0):e=!1,e||Pe(t)}function Mo(t){for(W=t.return;W;)switch(W.tag){case 5:case 13:Yt=!1;return;case 27:case 3:Yt=!0;return;default:W=W.return}}function Ba(t){if(t!==W)return!1;if(!ot)return Mo(t),ot=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||tc(t.type,t.memoizedProps)),n=!n),n&&$&&Pe(t),Mo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(p(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){$=Mi(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}$=null}}else e===27?(e=$,oa(t.type)?(t=ac,ac=null,$=t):$=e):$=W?Mi(t.stateNode.nextSibling):null;return!0}function Vn(){$=W=null,ot=!1}function wo(){var t=Bt;return t!==null&&(Ye===null?Ye=t:Ye.push.apply(Ye,t),Bt=null),t}function Xn(t){Bt===null?Bt=[t]:Bt.push(t)}var Ht=R(null),ci=null,bi=null;function Ri(t,e,n){k(Ht,e._currentValue),e._currentValue=n}function Si(t){t._currentValue=Ht.current,V(Ht)}function Za(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function dl(t,e,n,l){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){var f=o.child;u=u.firstContext;t:for(;u!==null;){var d=u;u=o;for(var v=0;v<e.length;v++)if(d.context===e[v]){u.lanes|=n,d=u.alternate,d!==null&&(d.lanes|=n),Za(u.return,n,t),l||(f=null);break t}u=d.next}}else if(o.tag===18){if(f=o.return,f===null)throw Error(p(341));f.lanes|=n,u=f.alternate,u!==null&&(u.lanes|=n),Za(f,n,t),f=null}else f=o.child;if(f!==null)f.return=o;else for(f=o;f!==null;){if(f===t){f=null;break}if(o=f.sibling,o!==null){o.return=f.return,f=o;break}f=f.return}o=f}}function Ua(t,e,n,l){t=null;for(var o=e,u=!1;o!==null;){if(!u){if((o.flags&524288)!==0)u=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var f=o.alternate;if(f===null)throw Error(p(387));if(f=f.memoizedProps,f!==null){var d=o.type;Ce(o.pendingProps.value,f.value)||(t!==null?t.push(d):t=[d])}}else if(o===kt.current){if(f=o.alternate,f===null)throw Error(p(387));f.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(Rs):t=[Rs])}o=o.return}t!==null&&dl(e,t,n,l),e.flags|=262144}function Eo(t){for(t=t.firstContext;t!==null;){if(!Ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ja(t){ci=t,bi=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ee(t){return Tc(ci,t)}function zo(t,e){return ci===null&&ja(t),Tc(t,e)}function Tc(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},bi===null){if(t===null)throw Error(p(308));bi=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else bi=bi.next=e;return n}var Nd=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Cd=g.unstable_scheduleCallback,Dd=g.unstable_NormalPriority,te={$$typeof:Mt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ku(){return{controller:new Nd,data:new Map,refCount:0}}function ss(t){t.refCount--,t.refCount===0&&Cd(Dd,function(){t.controller.abort()})}var os=null,Gu=0,ml=0,_l=null;function Rd(t,e){if(os===null){var n=os=[];Gu=0,ml=Vr(),_l={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Gu++,e.then(Mc,Mc),e}function Mc(){if(--Gu===0&&os!==null){_l!==null&&(_l.status="fulfilled");var t=os;os=null,ml=0,_l=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Bd(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(l.status="rejected",l.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),l}var wc=D.S;D.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Rd(t,e),wc!==null&&wc(t,e)};var Ha=R(null);function Yu(){var t=Ha.current;return t!==null?t:Zt.pooledCache}function Lo(t,e){e===null?k(Ha,Ha.current):k(Ha,e.pool)}function Ec(){var t=Yu();return t===null?null:{parent:te._currentValue,pool:t}}var us=Error(p(460)),zc=Error(p(474)),Ao=Error(p(542)),Vu={then:function(){}};function Lc(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Oo(){}function Ac(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Oo,Oo),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Nc(t),t;default:if(typeof e.status=="string")e.then(Oo,Oo);else{if(t=Zt,t!==null&&100<t.shellSuspendCounter)throw Error(p(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=l}},function(l){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Nc(t),t}throw rs=e,us}}var rs=null;function Oc(){if(rs===null)throw Error(p(459));var t=rs;return rs=null,t}function Nc(t){if(t===us||t===Ao)throw Error(p(483))}var Qn=!1;function Xu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Qu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Kn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Jn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(zt&2)!==0){var o=l.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),l.pending=e,e=Ci(t),Da(t,null,n),e}return Ca(t,l,e,n),Ci(t)}function cs(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ws(t,n)}}function Ku(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var o=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?o=u=f:u=u.next=f,n=n.next}while(n!==null);u===null?o=u=e:u=u.next=e}else o=u=e;n={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Ju=!1;function fs(){if(Ju){var t=_l;if(t!==null)throw t}}function hs(t,e,n,l){Ju=!1;var o=t.updateQueue;Qn=!1;var u=o.firstBaseUpdate,f=o.lastBaseUpdate,d=o.shared.pending;if(d!==null){o.shared.pending=null;var v=d,w=v.next;v.next=null,f===null?u=w:f.next=w,f=v;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,d=Z.lastBaseUpdate,d!==f&&(d===null?Z.firstBaseUpdate=w:d.next=w,Z.lastBaseUpdate=v))}if(u!==null){var j=o.baseState;f=0,Z=w=v=null,d=u;do{var z=d.lane&-536870913,A=z!==d.lane;if(A?(xt&z)===z:(l&z)===z){z!==0&&z===ml&&(Ju=!0),Z!==null&&(Z=Z.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var st=t,at=d;z=e;var Nt=n;switch(at.tag){case 1:if(st=at.payload,typeof st=="function"){j=st.call(Nt,j,z);break t}j=st;break t;case 3:st.flags=st.flags&-65537|128;case 0:if(st=at.payload,z=typeof st=="function"?st.call(Nt,j,z):st,z==null)break t;j=q({},j,z);break t;case 2:Qn=!0}}z=d.callback,z!==null&&(t.flags|=64,A&&(t.flags|=8192),A=o.callbacks,A===null?o.callbacks=[z]:A.push(z))}else A={lane:z,tag:d.tag,payload:d.payload,callback:d.callback,next:null},Z===null?(w=Z=A,v=j):Z=Z.next=A,f|=z;if(d=d.next,d===null){if(d=o.shared.pending,d===null)break;A=d,d=A.next,A.next=null,o.lastBaseUpdate=A,o.shared.pending=null}}while(!0);Z===null&&(v=j),o.baseState=v,o.firstBaseUpdate=w,o.lastBaseUpdate=Z,u===null&&(o.shared.lanes=0),na|=f,t.lanes=f,t.memoizedState=j}}function Cc(t,e){if(typeof t!="function")throw Error(p(191,t));t.call(e)}function Dc(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Cc(n[t],e)}var vl=R(null),No=R(0);function Rc(t,e){t=vn,k(No,t),k(vl,e),vn=t|e.baseLanes}function Wu(){k(No,vn),k(vl,vl.current)}function Fu(){vn=No.current,V(vl),V(No)}var Wn=0,dt=null,At=null,It=null,Co=!1,pl=!1,qa=!1,Do=0,ds=0,gl=null,Zd=0;function Jt(){throw Error(p(321))}function Iu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Ce(t[n],e[n]))return!1;return!0}function $u(t,e,n,l,o,u){return Wn=u,dt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,D.H=t===null||t.memoizedState===null?gf:yf,qa=!1,u=n(l,o),qa=!1,pl&&(u=Zc(e,n,l,o)),Bc(t),u}function Bc(t){D.H=Ho;var e=At!==null&&At.next!==null;if(Wn=0,It=At=dt=null,Co=!1,ds=0,gl=null,e)throw Error(p(300));t===null||ue||(t=t.dependencies,t!==null&&Eo(t)&&(ue=!0))}function Zc(t,e,n,l){dt=t;var o=0;do{if(pl&&(gl=null),ds=0,pl=!1,25<=o)throw Error(p(301));if(o+=1,It=At=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}D.H=Gd,u=e(n,l)}while(pl);return u}function Ud(){var t=D.H,e=t.useState()[0];return e=typeof e.then=="function"?ms(e):e,t=t.useState()[0],(At!==null?At.memoizedState:null)!==t&&(dt.flags|=1024),e}function tr(){var t=Do!==0;return Do=0,t}function er(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function ir(t){if(Co){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Co=!1}Wn=0,It=At=dt=null,pl=!1,ds=Do=0,gl=null}function ke(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return It===null?dt.memoizedState=It=t:It=It.next=t,It}function $t(){if(At===null){var t=dt.alternate;t=t!==null?t.memoizedState:null}else t=At.next;var e=It===null?dt.memoizedState:It.next;if(e!==null)It=e,At=t;else{if(t===null)throw dt.alternate===null?Error(p(467)):Error(p(310));At=t,t={memoizedState:At.memoizedState,baseState:At.baseState,baseQueue:At.baseQueue,queue:At.queue,next:null},It===null?dt.memoizedState=It=t:It=It.next=t}return It}function nr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ms(t){var e=ds;return ds+=1,gl===null&&(gl=[]),t=Ac(gl,t,e),e=dt,(It===null?e.memoizedState:It.next)===null&&(e=e.alternate,D.H=e===null||e.memoizedState===null?gf:yf),t}function Ro(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ms(t);if(t.$$typeof===Mt)return Ee(t)}throw Error(p(438,String(t)))}function ar(t){var e=null,n=dt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=dt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=nr(),dt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Hi;return e.index++,n}function cn(t,e){return typeof e=="function"?e(t):e}function Bo(t){var e=$t();return lr(e,At,t)}function lr(t,e,n){var l=t.queue;if(l===null)throw Error(p(311));l.lastRenderedReducer=n;var o=t.baseQueue,u=l.pending;if(u!==null){if(o!==null){var f=o.next;o.next=u.next,u.next=f}e.baseQueue=o=u,l.pending=null}if(u=t.baseState,o===null)t.memoizedState=u;else{e=o.next;var d=f=null,v=null,w=e,Z=!1;do{var j=w.lane&-536870913;if(j!==w.lane?(xt&j)===j:(Wn&j)===j){var z=w.revertLane;if(z===0)v!==null&&(v=v.next={lane:0,revertLane:0,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null}),j===ml&&(Z=!0);else if((Wn&z)===z){w=w.next,z===ml&&(Z=!0);continue}else j={lane:0,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},v===null?(d=v=j,f=u):v=v.next=j,dt.lanes|=z,na|=z;j=w.action,qa&&n(u,j),u=w.hasEagerState?w.eagerState:n(u,j)}else z={lane:j,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},v===null?(d=v=z,f=u):v=v.next=z,dt.lanes|=j,na|=j;w=w.next}while(w!==null&&w!==e);if(v===null?f=u:v.next=d,!Ce(u,t.memoizedState)&&(ue=!0,Z&&(n=_l,n!==null)))throw n;t.memoizedState=u,t.baseState=f,t.baseQueue=v,l.lastRenderedState=u}return o===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function sr(t){var e=$t(),n=e.queue;if(n===null)throw Error(p(311));n.lastRenderedReducer=t;var l=n.dispatch,o=n.pending,u=e.memoizedState;if(o!==null){n.pending=null;var f=o=o.next;do u=t(u,f.action),f=f.next;while(f!==o);Ce(u,e.memoizedState)||(ue=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),n.lastRenderedState=u}return[u,l]}function Uc(t,e,n){var l=dt,o=$t(),u=ot;if(u){if(n===void 0)throw Error(p(407));n=n()}else n=e();var f=!Ce((At||o).memoizedState,n);f&&(o.memoizedState=n,ue=!0),o=o.queue;var d=qc.bind(null,l,o,t);if(_s(2048,8,d,[t]),o.getSnapshot!==e||f||It!==null&&It.memoizedState.tag&1){if(l.flags|=2048,yl(9,Zo(),Hc.bind(null,l,o,n,e),null),Zt===null)throw Error(p(349));u||(Wn&124)!==0||jc(l,e,n)}return n}function jc(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=dt.updateQueue,e===null?(e=nr(),dt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Hc(t,e,n,l){e.value=n,e.getSnapshot=l,Pc(e)&&kc(t)}function qc(t,e,n){return n(function(){Pc(e)&&kc(t)})}function Pc(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Ce(t,n)}catch{return!0}}function kc(t){var e=Gn(t,2);e!==null&&ii(e,t,2)}function or(t){var e=ke();if(typeof t=="function"){var n=t;if(t=n(),qa){_i(!0);try{n()}finally{_i(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:t},e}function Gc(t,e,n,l){return t.baseState=n,lr(t,At,typeof l=="function"?l:cn)}function jd(t,e,n,l,o){if(jo(t))throw Error(p(485));if(t=e.action,t!==null){var u={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};D.T!==null?n(!0):u.isTransition=!1,l(u),n=e.pending,n===null?(u.next=e.pending=u,Yc(e,u)):(u.next=n.next,e.pending=n.next=u)}}function Yc(t,e){var n=e.action,l=e.payload,o=t.state;if(e.isTransition){var u=D.T,f={};D.T=f;try{var d=n(o,l),v=D.S;v!==null&&v(f,d),Vc(t,e,d)}catch(w){ur(t,e,w)}finally{D.T=u}}else try{u=n(o,l),Vc(t,e,u)}catch(w){ur(t,e,w)}}function Vc(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Xc(t,e,l)},function(l){return ur(t,e,l)}):Xc(t,e,n)}function Xc(t,e,n){e.status="fulfilled",e.value=n,Qc(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Yc(t,n)))}function ur(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Qc(e),e=e.next;while(e!==l)}t.action=null}function Qc(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Kc(t,e){return e}function Jc(t,e){if(ot){var n=Zt.formState;if(n!==null){t:{var l=dt;if(ot){if($){e:{for(var o=$,u=Yt;o.nodeType!==8;){if(!u){o=null;break e}if(o=Mi(o.nextSibling),o===null){o=null;break e}}u=o.data,o=u==="F!"||u==="F"?o:null}if(o){$=Mi(o.nextSibling),l=o.data==="F!";break t}}Pe(l)}l=!1}l&&(e=n[0])}}return n=ke(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Kc,lastRenderedState:e},n.queue=l,n=_f.bind(null,dt,l),l.dispatch=n,l=or(!1),u=dr.bind(null,dt,!1,l.queue),l=ke(),o={state:e,dispatch:null,action:t,pending:null},l.queue=o,n=jd.bind(null,dt,o,u,n),o.dispatch=n,l.memoizedState=t,[e,n,!1]}function Wc(t){var e=$t();return Fc(e,At,t)}function Fc(t,e,n){if(e=lr(t,e,Kc)[0],t=Bo(cn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ms(e)}catch(f){throw f===us?Ao:f}else l=e;e=$t();var o=e.queue,u=o.dispatch;return n!==e.memoizedState&&(dt.flags|=2048,yl(9,Zo(),Hd.bind(null,o,n),null)),[l,u,t]}function Hd(t,e){t.action=e}function Ic(t){var e=$t(),n=At;if(n!==null)return Fc(e,n,t);$t(),e=e.memoizedState,n=$t();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function yl(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=dt.updateQueue,e===null&&(e=nr(),dt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Zo(){return{destroy:void 0,resource:void 0}}function $c(){return $t().memoizedState}function Uo(t,e,n,l){var o=ke();l=l===void 0?null:l,dt.flags|=t,o.memoizedState=yl(1|e,Zo(),n,l)}function _s(t,e,n,l){var o=$t();l=l===void 0?null:l;var u=o.memoizedState.inst;At!==null&&l!==null&&Iu(l,At.memoizedState.deps)?o.memoizedState=yl(e,u,n,l):(dt.flags|=t,o.memoizedState=yl(1|e,u,n,l))}function tf(t,e){Uo(8390656,8,t,e)}function ef(t,e){_s(2048,8,t,e)}function nf(t,e){return _s(4,2,t,e)}function af(t,e){return _s(4,4,t,e)}function lf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function sf(t,e,n){n=n!=null?n.concat([t]):null,_s(4,4,lf.bind(null,e,t),n)}function rr(){}function of(t,e){var n=$t();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Iu(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function uf(t,e){var n=$t();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Iu(e,l[1]))return l[0];if(l=t(),qa){_i(!0);try{t()}finally{_i(!1)}}return n.memoizedState=[l,e],l}function cr(t,e,n){return n===void 0||(Wn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=fh(),dt.lanes|=t,na|=t,n)}function rf(t,e,n,l){return Ce(n,e)?n:vl.current!==null?(t=cr(t,n,l),Ce(t,e)||(ue=!0),t):(Wn&42)===0?(ue=!0,t.memoizedState=n):(t=fh(),dt.lanes|=t,na|=t,e)}function cf(t,e,n,l,o){var u=Q.p;Q.p=u!==0&&8>u?u:8;var f=D.T,d={};D.T=d,dr(t,!1,e,n);try{var v=o(),w=D.S;if(w!==null&&w(d,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var Z=Bd(v,l);vs(t,e,Z,ei(t))}else vs(t,e,l,ei(t))}catch(j){vs(t,e,{then:function(){},status:"rejected",reason:j},ei())}finally{Q.p=u,D.T=f}}function qd(){}function fr(t,e,n,l){if(t.tag!==5)throw Error(p(476));var o=ff(t).queue;cf(t,o,e,P,n===null?qd:function(){return hf(t),n(l)})}function ff(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:P},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function hf(t){var e=ff(t).next.queue;vs(t,e,{},ei())}function hr(){return Ee(Rs)}function df(){return $t().memoizedState}function mf(){return $t().memoizedState}function Pd(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ei();t=Kn(n);var l=Jn(e,t,n);l!==null&&(ii(l,e,n),cs(l,e,n)),e={cache:ku()},t.payload=e;return}e=e.return}}function kd(t,e,n){var l=ei();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},jo(t)?vf(e,n):(n=on(t,e,n,l),n!==null&&(ii(n,t,l),pf(n,e,l)))}function _f(t,e,n){var l=ei();vs(t,e,n,l)}function vs(t,e,n,l){var o={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(jo(t))vf(e,o);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var f=e.lastRenderedState,d=u(f,n);if(o.hasEagerState=!0,o.eagerState=d,Ce(d,f))return Ca(t,e,o,0),Zt===null&&Na(),!1}catch{}finally{}if(n=on(t,e,o,l),n!==null)return ii(n,t,l),pf(n,e,l),!0}return!1}function dr(t,e,n,l){if(l={lane:2,revertLane:Vr(),action:l,hasEagerState:!1,eagerState:null,next:null},jo(t)){if(e)throw Error(p(479))}else e=on(t,n,l,2),e!==null&&ii(e,t,2)}function jo(t){var e=t.alternate;return t===dt||e!==null&&e===dt}function vf(t,e){pl=Co=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function pf(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ws(t,n)}}var Ho={readContext:Ee,use:Ro,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},gf={readContext:Ee,use:Ro,useCallback:function(t,e){return ke().memoizedState=[t,e===void 0?null:e],t},useContext:Ee,useEffect:tf,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Uo(4194308,4,lf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Uo(4194308,4,t,e)},useInsertionEffect:function(t,e){Uo(4,2,t,e)},useMemo:function(t,e){var n=ke();e=e===void 0?null:e;var l=t();if(qa){_i(!0);try{t()}finally{_i(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=ke();if(n!==void 0){var o=n(e);if(qa){_i(!0);try{n(e)}finally{_i(!1)}}}else o=e;return l.memoizedState=l.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},l.queue=t,t=t.dispatch=kd.bind(null,dt,t),[l.memoizedState,t]},useRef:function(t){var e=ke();return t={current:t},e.memoizedState=t},useState:function(t){t=or(t);var e=t.queue,n=_f.bind(null,dt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:rr,useDeferredValue:function(t,e){var n=ke();return cr(n,t,e)},useTransition:function(){var t=or(!1);return t=cf.bind(null,dt,t.queue,!0,!1),ke().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=dt,o=ke();if(ot){if(n===void 0)throw Error(p(407));n=n()}else{if(n=e(),Zt===null)throw Error(p(349));(xt&124)!==0||jc(l,e,n)}o.memoizedState=n;var u={value:n,getSnapshot:e};return o.queue=u,tf(qc.bind(null,l,u,t),[t]),l.flags|=2048,yl(9,Zo(),Hc.bind(null,l,u,n,e),null),n},useId:function(){var t=ke(),e=Zt.identifierPrefix;if(ot){var n=_,l=h;n=(l&~(1<<32-Oe(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Do++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Zd++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:hr,useFormState:Jc,useActionState:Jc,useOptimistic:function(t){var e=ke();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=dr.bind(null,dt,!0,n),n.dispatch=e,[t,e]},useMemoCache:ar,useCacheRefresh:function(){return ke().memoizedState=Pd.bind(null,dt)}},yf={readContext:Ee,use:Ro,useCallback:of,useContext:Ee,useEffect:ef,useImperativeHandle:sf,useInsertionEffect:nf,useLayoutEffect:af,useMemo:uf,useReducer:Bo,useRef:$c,useState:function(){return Bo(cn)},useDebugValue:rr,useDeferredValue:function(t,e){var n=$t();return rf(n,At.memoizedState,t,e)},useTransition:function(){var t=Bo(cn)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:ms(t),e]},useSyncExternalStore:Uc,useId:df,useHostTransitionStatus:hr,useFormState:Wc,useActionState:Wc,useOptimistic:function(t,e){var n=$t();return Gc(n,At,t,e)},useMemoCache:ar,useCacheRefresh:mf},Gd={readContext:Ee,use:Ro,useCallback:of,useContext:Ee,useEffect:ef,useImperativeHandle:sf,useInsertionEffect:nf,useLayoutEffect:af,useMemo:uf,useReducer:sr,useRef:$c,useState:function(){return sr(cn)},useDebugValue:rr,useDeferredValue:function(t,e){var n=$t();return At===null?cr(n,t,e):rf(n,At.memoizedState,t,e)},useTransition:function(){var t=sr(cn)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:ms(t),e]},useSyncExternalStore:Uc,useId:df,useHostTransitionStatus:hr,useFormState:Ic,useActionState:Ic,useOptimistic:function(t,e){var n=$t();return At!==null?Gc(n,At,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:ar,useCacheRefresh:mf},xl=null,ps=0;function qo(t){var e=ps;return ps+=1,xl===null&&(xl=[]),Ac(xl,t,e)}function gs(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Po(t,e){throw e.$$typeof===tt?Error(p(525)):(t=Object.prototype.toString.call(e),Error(p(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function xf(t){var e=t._init;return e(t._payload)}function bf(t){function e(S,y){if(t){var M=S.deletions;M===null?(S.deletions=[y],S.flags|=16):M.push(y)}}function n(S,y){if(!t)return null;for(;y!==null;)e(S,y),y=y.sibling;return null}function l(S){for(var y=new Map;S!==null;)S.key!==null?y.set(S.key,S):y.set(S.index,S),S=S.sibling;return y}function o(S,y){return S=ri(S,y),S.index=0,S.sibling=null,S}function u(S,y,M){return S.index=M,t?(M=S.alternate,M!==null?(M=M.index,M<y?(S.flags|=67108866,y):M):(S.flags|=67108866,y)):(S.flags|=1048576,y)}function f(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function d(S,y,M,U){return y===null||y.tag!==6?(y=ls(M,S.mode,U),y.return=S,y):(y=o(y,M),y.return=S,y)}function v(S,y,M,U){var F=M.type;return F===Ut?Z(S,y,M.props.children,U,M.key):y!==null&&(y.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===be&&xf(F)===y.type)?(y=o(y,M.props),gs(y,M),y.return=S,y):(y=Ra(M.type,M.key,M.props,null,S.mode,U),gs(y,M),y.return=S,y)}function w(S,y,M,U){return y===null||y.tag!==4||y.stateNode.containerInfo!==M.containerInfo||y.stateNode.implementation!==M.implementation?(y=hl(M,S.mode,U),y.return=S,y):(y=o(y,M.children||[]),y.return=S,y)}function Z(S,y,M,U,F){return y===null||y.tag!==7?(y=Di(M,S.mode,U,F),y.return=S,y):(y=o(y,M),y.return=S,y)}function j(S,y,M){if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return y=ls(""+y,S.mode,M),y.return=S,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ct:return M=Ra(y.type,y.key,y.props,null,S.mode,M),gs(M,y),M.return=S,M;case Pt:return y=hl(y,S.mode,M),y.return=S,y;case be:var U=y._init;return y=U(y._payload),j(S,y,M)}if(ae(y)||Et(y))return y=Di(y,S.mode,M,null),y.return=S,y;if(typeof y.then=="function")return j(S,qo(y),M);if(y.$$typeof===Mt)return j(S,zo(S,y),M);Po(S,y)}return null}function z(S,y,M,U){var F=y!==null?y.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return F!==null?null:d(S,y,""+M,U);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case ct:return M.key===F?v(S,y,M,U):null;case Pt:return M.key===F?w(S,y,M,U):null;case be:return F=M._init,M=F(M._payload),z(S,y,M,U)}if(ae(M)||Et(M))return F!==null?null:Z(S,y,M,U,null);if(typeof M.then=="function")return z(S,y,qo(M),U);if(M.$$typeof===Mt)return z(S,y,zo(S,M),U);Po(S,M)}return null}function A(S,y,M,U,F){if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return S=S.get(M)||null,d(y,S,""+U,F);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case ct:return S=S.get(U.key===null?M:U.key)||null,v(y,S,U,F);case Pt:return S=S.get(U.key===null?M:U.key)||null,w(y,S,U,F);case be:var vt=U._init;return U=vt(U._payload),A(S,y,M,U,F)}if(ae(U)||Et(U))return S=S.get(M)||null,Z(y,S,U,F,null);if(typeof U.then=="function")return A(S,y,M,qo(U),F);if(U.$$typeof===Mt)return A(S,y,M,zo(y,U),F);Po(y,U)}return null}function st(S,y,M,U){for(var F=null,vt=null,it=y,lt=y=0,ce=null;it!==null&&lt<M.length;lt++){it.index>lt?(ce=it,it=null):ce=it.sibling;var Tt=z(S,it,M[lt],U);if(Tt===null){it===null&&(it=ce);break}t&&it&&Tt.alternate===null&&e(S,it),y=u(Tt,y,lt),vt===null?F=Tt:vt.sibling=Tt,vt=Tt,it=ce}if(lt===M.length)return n(S,it),ot&&T(S,lt),F;if(it===null){for(;lt<M.length;lt++)it=j(S,M[lt],U),it!==null&&(y=u(it,y,lt),vt===null?F=it:vt.sibling=it,vt=it);return ot&&T(S,lt),F}for(it=l(it);lt<M.length;lt++)ce=A(it,S,lt,M[lt],U),ce!==null&&(t&&ce.alternate!==null&&it.delete(ce.key===null?lt:ce.key),y=u(ce,y,lt),vt===null?F=ce:vt.sibling=ce,vt=ce);return t&&it.forEach(function(ha){return e(S,ha)}),ot&&T(S,lt),F}function at(S,y,M,U){if(M==null)throw Error(p(151));for(var F=null,vt=null,it=y,lt=y=0,ce=null,Tt=M.next();it!==null&&!Tt.done;lt++,Tt=M.next()){it.index>lt?(ce=it,it=null):ce=it.sibling;var ha=z(S,it,Tt.value,U);if(ha===null){it===null&&(it=ce);break}t&&it&&ha.alternate===null&&e(S,it),y=u(ha,y,lt),vt===null?F=ha:vt.sibling=ha,vt=ha,it=ce}if(Tt.done)return n(S,it),ot&&T(S,lt),F;if(it===null){for(;!Tt.done;lt++,Tt=M.next())Tt=j(S,Tt.value,U),Tt!==null&&(y=u(Tt,y,lt),vt===null?F=Tt:vt.sibling=Tt,vt=Tt);return ot&&T(S,lt),F}for(it=l(it);!Tt.done;lt++,Tt=M.next())Tt=A(it,S,lt,Tt.value,U),Tt!==null&&(t&&Tt.alternate!==null&&it.delete(Tt.key===null?lt:Tt.key),y=u(Tt,y,lt),vt===null?F=Tt:vt.sibling=Tt,vt=Tt);return t&&it.forEach(function(Ym){return e(S,Ym)}),ot&&T(S,lt),F}function Nt(S,y,M,U){if(typeof M=="object"&&M!==null&&M.type===Ut&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case ct:t:{for(var F=M.key;y!==null;){if(y.key===F){if(F=M.type,F===Ut){if(y.tag===7){n(S,y.sibling),U=o(y,M.props.children),U.return=S,S=U;break t}}else if(y.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===be&&xf(F)===y.type){n(S,y.sibling),U=o(y,M.props),gs(U,M),U.return=S,S=U;break t}n(S,y);break}else e(S,y);y=y.sibling}M.type===Ut?(U=Di(M.props.children,S.mode,U,M.key),U.return=S,S=U):(U=Ra(M.type,M.key,M.props,null,S.mode,U),gs(U,M),U.return=S,S=U)}return f(S);case Pt:t:{for(F=M.key;y!==null;){if(y.key===F)if(y.tag===4&&y.stateNode.containerInfo===M.containerInfo&&y.stateNode.implementation===M.implementation){n(S,y.sibling),U=o(y,M.children||[]),U.return=S,S=U;break t}else{n(S,y);break}else e(S,y);y=y.sibling}U=hl(M,S.mode,U),U.return=S,S=U}return f(S);case be:return F=M._init,M=F(M._payload),Nt(S,y,M,U)}if(ae(M))return st(S,y,M,U);if(Et(M)){if(F=Et(M),typeof F!="function")throw Error(p(150));return M=F.call(M),at(S,y,M,U)}if(typeof M.then=="function")return Nt(S,y,qo(M),U);if(M.$$typeof===Mt)return Nt(S,y,zo(S,M),U);Po(S,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,y!==null&&y.tag===6?(n(S,y.sibling),U=o(y,M),U.return=S,S=U):(n(S,y),U=ls(M,S.mode,U),U.return=S,S=U),f(S)):n(S,y)}return function(S,y,M,U){try{ps=0;var F=Nt(S,y,M,U);return xl=null,F}catch(it){if(it===us||it===Ao)throw it;var vt=Ze(29,it,null,S.mode);return vt.lanes=U,vt.return=S,vt}finally{}}}var bl=bf(!0),Sf=bf(!1),fi=R(null),Bi=null;function Fn(t){var e=t.alternate;k(ee,ee.current&1),k(fi,t),Bi===null&&(e===null||vl.current!==null||e.memoizedState!==null)&&(Bi=t)}function Tf(t){if(t.tag===22){if(k(ee,ee.current),k(fi,t),Bi===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Bi=t)}}else In()}function In(){k(ee,ee.current),k(fi,fi.current)}function fn(t){V(fi),Bi===t&&(Bi=null),V(ee)}var ee=R(0);function ko(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||nc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function mr(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:q({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var _r={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ei(),o=Kn(l);o.payload=e,n!=null&&(o.callback=n),e=Jn(t,o,l),e!==null&&(ii(e,t,l),cs(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ei(),o=Kn(l);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=Jn(t,o,l),e!==null&&(ii(e,t,l),cs(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ei(),l=Kn(n);l.tag=2,e!=null&&(l.callback=e),e=Jn(t,l,n),e!==null&&(ii(e,t,n),cs(e,t,n))}};function Mf(t,e,n,l,o,u,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,f):e.prototype&&e.prototype.isPureReactComponent?!De(n,l)||!De(o,u):!0}function wf(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&_r.enqueueReplaceState(e,e.state,null)}function Pa(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=q({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var Go=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ef(t){Go(t)}function zf(t){console.error(t)}function Lf(t){Go(t)}function Yo(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Af(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function vr(t,e,n){return n=Kn(n),n.tag=3,n.payload={element:null},n.callback=function(){Yo(t,e)},n}function Of(t){return t=Kn(t),t.tag=3,t}function Nf(t,e,n,l){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var u=l.value;t.payload=function(){return o(u)},t.callback=function(){Af(e,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Af(e,n,l),typeof o!="function"&&(aa===null?aa=new Set([this]):aa.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function Yd(t,e,n,l,o){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Ua(e,n,o,!0),n=fi.current,n!==null){switch(n.tag){case 13:return Bi===null?qr():n.alternate===null&&Kt===0&&(Kt=3),n.flags&=-257,n.flags|=65536,n.lanes=o,l===Vu?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),kr(t,l,o)),!1;case 22:return n.flags|=65536,l===Vu?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),kr(t,l,o)),!1}throw Error(p(435,n.tag))}return kr(t,l,o),qr(),!1}if(ot)return e=fi.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,l!==oe&&(t=Error(p(422),{cause:l}),Xn(Re(t,n)))):(l!==oe&&(e=Error(p(423),{cause:l}),Xn(Re(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,l=Re(l,n),o=vr(t.stateNode,l,o),Ku(t,o),Kt!==4&&(Kt=2)),!1;var u=Error(p(520),{cause:l});if(u=Re(u,n),ws===null?ws=[u]:ws.push(u),Kt!==4&&(Kt=2),e===null)return!0;l=Re(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=vr(n.stateNode,l,t),Ku(n,t),!1;case 1:if(e=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(aa===null||!aa.has(u))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Of(o),Nf(o,t,n,l),Ku(n,o),!1}n=n.return}while(n!==null);return!1}var Cf=Error(p(461)),ue=!1;function pe(t,e,n,l){e.child=t===null?Sf(e,null,n,l):bl(e,t.child,n,l)}function Df(t,e,n,l,o){n=n.render;var u=e.ref;if("ref"in l){var f={};for(var d in l)d!=="ref"&&(f[d]=l[d])}else f=l;return ja(e),l=$u(t,e,n,f,u,o),d=tr(),t!==null&&!ue?(er(t,e,o),hn(t,e,o)):(ot&&d&&H(e),e.flags|=1,pe(t,e,l,o),e.child)}function Rf(t,e,n,l,o){if(t===null){var u=n.type;return typeof u=="function"&&!fl(u)&&u.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=u,Bf(t,e,u,l,o)):(t=Ra(n.type,null,l,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Mr(t,o)){var f=u.memoizedProps;if(n=n.compare,n=n!==null?n:De,n(f,l)&&t.ref===e.ref)return hn(t,e,o)}return e.flags|=1,t=ri(u,l),t.ref=e.ref,t.return=e,e.child=t}function Bf(t,e,n,l,o){if(t!==null){var u=t.memoizedProps;if(De(u,l)&&t.ref===e.ref)if(ue=!1,e.pendingProps=l=u,Mr(t,o))(t.flags&131072)!==0&&(ue=!0);else return e.lanes=t.lanes,hn(t,e,o)}return pr(t,e,n,l,o)}function Zf(t,e,n){var l=e.pendingProps,o=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=u!==null?u.baseLanes|n:n,t!==null){for(o=e.child=t.child,u=0;o!==null;)u=u|o.lanes|o.childLanes,o=o.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Uf(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Lo(e,u!==null?u.cachePool:null),u!==null?Rc(e,u):Wu(),Tf(e);else return e.lanes=e.childLanes=536870912,Uf(t,e,u!==null?u.baseLanes|n:n,n)}else u!==null?(Lo(e,u.cachePool),Rc(e,u),In(),e.memoizedState=null):(t!==null&&Lo(e,null),Wu(),In());return pe(t,e,o,n),e.child}function Uf(t,e,n,l){var o=Yu();return o=o===null?null:{parent:te._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&Lo(e,null),Wu(),Tf(e),t!==null&&Ua(t,e,l,!0),null}function Vo(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(p(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function pr(t,e,n,l,o){return ja(e),n=$u(t,e,n,l,void 0,o),l=tr(),t!==null&&!ue?(er(t,e,o),hn(t,e,o)):(ot&&l&&H(e),e.flags|=1,pe(t,e,n,o),e.child)}function jf(t,e,n,l,o,u){return ja(e),e.updateQueue=null,n=Zc(e,l,n,o),Bc(t),l=tr(),t!==null&&!ue?(er(t,e,u),hn(t,e,u)):(ot&&l&&H(e),e.flags|=1,pe(t,e,n,u),e.child)}function Hf(t,e,n,l,o){if(ja(e),e.stateNode===null){var u=un,f=n.contextType;typeof f=="object"&&f!==null&&(u=Ee(f)),u=new n(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=_r,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},Xu(e),f=n.contextType,u.context=typeof f=="object"&&f!==null?Ee(f):un,u.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(mr(e,n,f,l),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&_r.enqueueReplaceState(u,u.state,null),hs(e,l,u,o),fs(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var d=e.memoizedProps,v=Pa(n,d);u.props=v;var w=u.context,Z=n.contextType;f=un,typeof Z=="object"&&Z!==null&&(f=Ee(Z));var j=n.getDerivedStateFromProps;Z=typeof j=="function"||typeof u.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,Z||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d||w!==f)&&wf(e,u,l,f),Qn=!1;var z=e.memoizedState;u.state=z,hs(e,l,u,o),fs(),w=e.memoizedState,d||z!==w||Qn?(typeof j=="function"&&(mr(e,n,j,l),w=e.memoizedState),(v=Qn||Mf(e,n,v,l,z,w,f))?(Z||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=w),u.props=l,u.state=w,u.context=f,l=v):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,Qu(t,e),f=e.memoizedProps,Z=Pa(n,f),u.props=Z,j=e.pendingProps,z=u.context,w=n.contextType,v=un,typeof w=="object"&&w!==null&&(v=Ee(w)),d=n.getDerivedStateFromProps,(w=typeof d=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==j||z!==v)&&wf(e,u,l,v),Qn=!1,z=e.memoizedState,u.state=z,hs(e,l,u,o),fs();var A=e.memoizedState;f!==j||z!==A||Qn||t!==null&&t.dependencies!==null&&Eo(t.dependencies)?(typeof d=="function"&&(mr(e,n,d,l),A=e.memoizedState),(Z=Qn||Mf(e,n,Z,l,z,A,v)||t!==null&&t.dependencies!==null&&Eo(t.dependencies))?(w||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,A,v),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,A,v)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=A),u.props=l,u.state=A,u.context=v,l=Z):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Vo(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=bl(e,t.child,null,o),e.child=bl(e,null,n,o)):pe(t,e,n,o),e.memoizedState=u.state,t=e.child):t=hn(t,e,o),t}function qf(t,e,n,l){return Vn(),e.flags|=256,pe(t,e,n,l),e.child}var gr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function yr(t){return{baseLanes:t,cachePool:Ec()}}function xr(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=hi),t}function Pf(t,e,n){var l=e.pendingProps,o=!1,u=(e.flags&128)!==0,f;if((f=u)||(f=t!==null&&t.memoizedState===null?!1:(ee.current&2)!==0),f&&(o=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(ot){if(o?Fn(e):In(),ot){var d=$,v;if(v=d){t:{for(v=d,d=Yt;v.nodeType!==8;){if(!d){d=null;break t}if(v=Mi(v.nextSibling),v===null){d=null;break t}}d=v}d!==null?(e.memoizedState={dehydrated:d,treeContext:c!==null?{id:h,overflow:_}:null,retryLane:536870912,hydrationErrors:null},v=Ze(18,null,null,0),v.stateNode=d,v.return=e,e.child=v,W=e,$=null,v=!0):v=!1}v||Pe(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return nc(d)?e.lanes=32:e.lanes=536870912,null;fn(e)}return d=l.children,l=l.fallback,o?(In(),o=e.mode,d=Xo({mode:"hidden",children:d},o),l=Di(l,o,n,null),d.return=e,l.return=e,d.sibling=l,e.child=d,o=e.child,o.memoizedState=yr(n),o.childLanes=xr(t,f,n),e.memoizedState=gr,l):(Fn(e),br(e,d))}if(v=t.memoizedState,v!==null&&(d=v.dehydrated,d!==null)){if(u)e.flags&256?(Fn(e),e.flags&=-257,e=Sr(t,e,n)):e.memoizedState!==null?(In(),e.child=t.child,e.flags|=128,e=null):(In(),o=l.fallback,d=e.mode,l=Xo({mode:"visible",children:l.children},d),o=Di(o,d,n,null),o.flags|=2,l.return=e,o.return=e,l.sibling=o,e.child=l,bl(e,t.child,null,n),l=e.child,l.memoizedState=yr(n),l.childLanes=xr(t,f,n),e.memoizedState=gr,e=o);else if(Fn(e),nc(d)){if(f=d.nextSibling&&d.nextSibling.dataset,f)var w=f.dgst;f=w,l=Error(p(419)),l.stack="",l.digest=f,Xn({value:l,source:null,stack:null}),e=Sr(t,e,n)}else if(ue||Ua(t,e,n,!1),f=(n&t.childLanes)!==0,ue||f){if(f=Zt,f!==null&&(l=n&-n,l=(l&42)!==0?1:Hl(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==v.retryLane))throw v.retryLane=l,Gn(t,l),ii(f,t,l),Cf;d.data==="$?"||qr(),e=Sr(t,e,n)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=v.treeContext,$=Mi(d.nextSibling),W=e,ot=!0,Bt=null,Yt=!1,t!==null&&(s[r++]=h,s[r++]=_,s[r++]=c,h=t.id,_=t.overflow,c=e),e=br(e,l.children),e.flags|=4096);return e}return o?(In(),o=l.fallback,d=e.mode,v=t.child,w=v.sibling,l=ri(v,{mode:"hidden",children:l.children}),l.subtreeFlags=v.subtreeFlags&65011712,w!==null?o=ri(w,o):(o=Di(o,d,n,null),o.flags|=2),o.return=e,l.return=e,l.sibling=o,e.child=l,l=o,o=e.child,d=t.child.memoizedState,d===null?d=yr(n):(v=d.cachePool,v!==null?(w=te._currentValue,v=v.parent!==w?{parent:w,pool:w}:v):v=Ec(),d={baseLanes:d.baseLanes|n,cachePool:v}),o.memoizedState=d,o.childLanes=xr(t,f,n),e.memoizedState=gr,l):(Fn(e),n=t.child,t=n.sibling,n=ri(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function br(t,e){return e=Xo({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Xo(t,e){return t=Ze(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Sr(t,e,n){return bl(e,t.child,null,n),t=br(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function kf(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Za(t.return,e,n)}function Tr(t,e,n,l,o){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:o}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=n,u.tailMode=o)}function Gf(t,e,n){var l=e.pendingProps,o=l.revealOrder,u=l.tail;if(pe(t,e,l.children,n),l=ee.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&kf(t,n,e);else if(t.tag===19)kf(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(k(ee,l),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&ko(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),Tr(e,!1,o,n,u);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&ko(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}Tr(e,!0,n,null,u);break;case"together":Tr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function hn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),na|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Ua(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(p(153));if(e.child!==null){for(t=e.child,n=ri(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=ri(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Mr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Eo(t)))}function Vd(t,e,n){switch(e.tag){case 3:bt(e,e.stateNode.containerInfo),Ri(e,te,t.memoizedState.cache),Vn();break;case 27:case 5:ma(e);break;case 4:bt(e,e.stateNode.containerInfo);break;case 10:Ri(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Fn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Pf(t,e,n):(Fn(e),t=hn(t,e,n),t!==null?t.sibling:null);Fn(e);break;case 19:var o=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Ua(t,e,n,!1),l=(n&e.childLanes)!==0),o){if(l)return Gf(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),k(ee,ee.current),l)break;return null;case 22:case 23:return e.lanes=0,Zf(t,e,n);case 24:Ri(e,te,t.memoizedState.cache)}return hn(t,e,n)}function Yf(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)ue=!0;else{if(!Mr(t,n)&&(e.flags&128)===0)return ue=!1,Vd(t,e,n);ue=(t.flags&131072)!==0}else ue=!1,ot&&(e.flags&1048576)!==0&&O(e,a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,o=l._init;if(l=o(l._payload),e.type=l,typeof l=="function")fl(l)?(t=Pa(l,t),e.tag=1,e=Hf(null,e,l,t,n)):(e.tag=0,e=pr(null,e,l,t,n));else{if(l!=null){if(o=l.$$typeof,o===fe){e.tag=11,e=Df(null,e,l,t,n);break t}else if(o===xe){e.tag=14,e=Rf(null,e,l,t,n);break t}}throw e=de(l)||l,Error(p(306,e,""))}}return e;case 0:return pr(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,o=Pa(l,e.pendingProps),Hf(t,e,l,o,n);case 3:t:{if(bt(e,e.stateNode.containerInfo),t===null)throw Error(p(387));l=e.pendingProps;var u=e.memoizedState;o=u.element,Qu(t,e),hs(e,l,null,n);var f=e.memoizedState;if(l=f.cache,Ri(e,te,l),l!==u.cache&&dl(e,[te],n,!0),fs(),l=f.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=qf(t,e,l,n);break t}else if(l!==o){o=Re(Error(p(424)),e),Xn(o),e=qf(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for($=Mi(t.firstChild),W=e,ot=!0,Bt=null,Yt=!0,n=Sf(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Vn(),l===o){e=hn(t,e,n);break t}pe(t,e,l,n)}e=e.child}return e;case 26:return Vo(t,e),t===null?(n=Kh(e.type,null,e.pendingProps,null))?e.memoizedState=n:ot||(n=e.type,t=e.pendingProps,l=su(X.current).createElement(n),l[le]=e,l[Te]=t,ye(l,n,t),Ft(l),e.stateNode=l):e.memoizedState=Kh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ma(e),t===null&&ot&&(l=e.stateNode=Vh(e.type,e.pendingProps,X.current),W=e,Yt=!0,o=$,oa(e.type)?(ac=o,$=Mi(l.firstChild)):$=o),pe(t,e,e.pendingProps.children,n),Vo(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ot&&((o=l=$)&&(l=ym(l,e.type,e.pendingProps,Yt),l!==null?(e.stateNode=l,W=e,$=Mi(l.firstChild),Yt=!1,o=!0):o=!1),o||Pe(e)),ma(e),o=e.type,u=e.pendingProps,f=t!==null?t.memoizedProps:null,l=u.children,tc(o,u)?l=null:f!==null&&tc(o,f)&&(e.flags|=32),e.memoizedState!==null&&(o=$u(t,e,Ud,null,null,n),Rs._currentValue=o),Vo(t,e),pe(t,e,l,n),e.child;case 6:return t===null&&ot&&((t=n=$)&&(n=xm(n,e.pendingProps,Yt),n!==null?(e.stateNode=n,W=e,$=null,t=!0):t=!1),t||Pe(e)),null;case 13:return Pf(t,e,n);case 4:return bt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=bl(e,null,l,n):pe(t,e,l,n),e.child;case 11:return Df(t,e,e.type,e.pendingProps,n);case 7:return pe(t,e,e.pendingProps,n),e.child;case 8:return pe(t,e,e.pendingProps.children,n),e.child;case 12:return pe(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,Ri(e,e.type,l.value),pe(t,e,l.children,n),e.child;case 9:return o=e.type._context,l=e.pendingProps.children,ja(e),o=Ee(o),l=l(o),e.flags|=1,pe(t,e,l,n),e.child;case 14:return Rf(t,e,e.type,e.pendingProps,n);case 15:return Bf(t,e,e.type,e.pendingProps,n);case 19:return Gf(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Xo(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=ri(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Zf(t,e,n);case 24:return ja(e),l=Ee(te),t===null?(o=Yu(),o===null&&(o=Zt,u=ku(),o.pooledCache=u,u.refCount++,u!==null&&(o.pooledCacheLanes|=n),o=u),e.memoizedState={parent:l,cache:o},Xu(e),Ri(e,te,o)):((t.lanes&n)!==0&&(Qu(t,e),hs(e,null,null,n),fs()),o=t.memoizedState,u=e.memoizedState,o.parent!==l?(o={parent:l,cache:l},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),Ri(e,te,l)):(l=u.cache,Ri(e,te,l),l!==o.cache&&dl(e,[te],n,!0))),pe(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(p(156,e.tag))}function dn(t){t.flags|=4}function Vf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!$h(e)){if(e=fi.current,e!==null&&((xt&4194048)===xt?Bi!==null:(xt&62914560)!==xt&&(xt&536870912)===0||e!==Bi))throw rs=Vu,zc;t.flags|=8192}}function Qo(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?jl():536870912,t.lanes|=e,wl|=e)}function ys(t,e){if(!ot)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Vt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function Xd(t,e,n){var l=e.pendingProps;switch(K(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Vt(e),null;case 1:return Vt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Si(te),mi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ba(e)?dn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,wo())),Vt(e),null;case 26:return n=e.memoizedState,t===null?(dn(e),n!==null?(Vt(e),Vf(e,n)):(Vt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(dn(e),Vt(e),Vf(e,n)):(Vt(e),e.flags&=-16777217):(t.memoizedProps!==l&&dn(e),Vt(e),e.flags&=-16777217),null;case 27:yn(e),n=X.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(!l){if(e.stateNode===null)throw Error(p(166));return Vt(e),null}t=J.current,Ba(e)?To(e):(t=Vh(o,l,n),e.stateNode=t,dn(e))}return Vt(e),null;case 5:if(yn(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(!l){if(e.stateNode===null)throw Error(p(166));return Vt(e),null}if(t=J.current,Ba(e))To(e);else{switch(o=su(X.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?o.createElement(n,{is:l.is}):o.createElement(n)}}t[le]=e,t[Te]=l;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(ye(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&dn(e)}}return Vt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(p(166));if(t=X.current,Ba(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,o=W,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}t[le]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||jh(t.nodeValue,n)),t||Pe(e)}else t=su(t).createTextNode(l),t[le]=e,e.stateNode=t}return Vt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ba(e),l!==null&&l.dehydrated!==null){if(t===null){if(!o)throw Error(p(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(p(317));o[le]=e}else Vn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Vt(e),o=!1}else o=wo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(fn(e),e):(fn(e),null)}if(fn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==o&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Qo(e,e.updateQueue),Vt(e),null;case 4:return mi(),t===null&&Jr(e.stateNode.containerInfo),Vt(e),null;case 10:return Si(e.type),Vt(e),null;case 19:if(V(ee),o=e.memoizedState,o===null)return Vt(e),null;if(l=(e.flags&128)!==0,u=o.rendering,u===null)if(l)ys(o,!1);else{if(Kt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=ko(t),u!==null){for(e.flags|=128,ys(o,!1),t=u.updateQueue,e.updateQueue=t,Qo(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)as(n,t),n=n.sibling;return k(ee,ee.current&1|2),e.child}t=t.sibling}o.tail!==null&&je()>Wo&&(e.flags|=128,l=!0,ys(o,!1),e.lanes=4194304)}else{if(!l)if(t=ko(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Qo(e,t),ys(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!ot)return Vt(e),null}else 2*je()-o.renderingStartTime>Wo&&n!==536870912&&(e.flags|=128,l=!0,ys(o,!1),e.lanes=4194304);o.isBackwards?(u.sibling=e.child,e.child=u):(t=o.last,t!==null?t.sibling=u:e.child=u,o.last=u)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=je(),e.sibling=null,t=ee.current,k(ee,l?t&1|2:t&1),e):(Vt(e),null);case 22:case 23:return fn(e),Fu(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Vt(e),e.subtreeFlags&6&&(e.flags|=8192)):Vt(e),n=e.updateQueue,n!==null&&Qo(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&V(Ha),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Si(te),Vt(e),null;case 25:return null;case 30:return null}throw Error(p(156,e.tag))}function Qd(t,e){switch(K(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Si(te),mi(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return yn(e),null;case 13:if(fn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(p(340));Vn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return V(ee),null;case 4:return mi(),null;case 10:return Si(e.type),null;case 22:case 23:return fn(e),Fu(),t!==null&&V(Ha),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Si(te),null;case 25:return null;default:return null}}function Xf(t,e){switch(K(e),e.tag){case 3:Si(te),mi();break;case 26:case 27:case 5:yn(e);break;case 4:mi();break;case 13:fn(e);break;case 19:V(ee);break;case 10:Si(e.type);break;case 22:case 23:fn(e),Fu(),t!==null&&V(Ha);break;case 24:Si(te)}}function xs(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var o=l.next;n=o;do{if((n.tag&t)===t){l=void 0;var u=n.create,f=n.inst;l=u(),f.destroy=l}n=n.next}while(n!==o)}}catch(d){Ct(e,e.return,d)}}function $n(t,e,n){try{var l=e.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var u=o.next;l=u;do{if((l.tag&t)===t){var f=l.inst,d=f.destroy;if(d!==void 0){f.destroy=void 0,o=e;var v=n,w=d;try{w()}catch(Z){Ct(o,v,Z)}}}l=l.next}while(l!==u)}}catch(Z){Ct(e,e.return,Z)}}function Qf(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Dc(e,n)}catch(l){Ct(t,t.return,l)}}}function Kf(t,e,n){n.props=Pa(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Ct(t,e,l)}}function bs(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(o){Ct(t,e,o)}}function Zi(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(o){Ct(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){Ct(t,e,o)}else n.current=null}function Jf(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(o){Ct(t,t.return,o)}}function wr(t,e,n){try{var l=t.stateNode;mm(l,t.type,n,e),l[Te]=e}catch(o){Ct(t,t.return,o)}}function Wf(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&oa(t.type)||t.tag===4}function Er(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Wf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&oa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zr(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=lu));else if(l!==4&&(l===27&&oa(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(zr(t,e,n),t=t.sibling;t!==null;)zr(t,e,n),t=t.sibling}function Ko(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&oa(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Ko(t,e,n),t=t.sibling;t!==null;)Ko(t,e,n),t=t.sibling}function Ff(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);ye(e,l,n),e[le]=t,e[Te]=n}catch(u){Ct(t,t.return,u)}}var mn=!1,Wt=!1,Lr=!1,If=typeof WeakSet=="function"?WeakSet:Set,re=null;function Kd(t,e){if(t=t.containerInfo,Ir=hu,t=La(t),Aa(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var o=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break t}var f=0,d=-1,v=-1,w=0,Z=0,j=t,z=null;e:for(;;){for(var A;j!==n||o!==0&&j.nodeType!==3||(d=f+o),j!==u||l!==0&&j.nodeType!==3||(v=f+l),j.nodeType===3&&(f+=j.nodeValue.length),(A=j.firstChild)!==null;)z=j,j=A;for(;;){if(j===t)break e;if(z===n&&++w===o&&(d=f),z===u&&++Z===l&&(v=f),(A=j.nextSibling)!==null)break;j=z,z=j.parentNode}j=A}n=d===-1||v===-1?null:{start:d,end:v}}else n=null}n=n||{start:0,end:0}}else n=null;for($r={focusedElem:t,selectionRange:n},hu=!1,re=e;re!==null;)if(e=re,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,re=t;else for(;re!==null;){switch(e=re,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,n=e,o=u.memoizedProps,u=u.memoizedState,l=n.stateNode;try{var st=Pa(n.type,o,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(st,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(at){Ct(n,n.return,at)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ic(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ic(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(p(163))}if(t=e.sibling,t!==null){t.return=e.return,re=t;break}re=e.return}}function $f(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:ta(t,n),l&4&&xs(5,n);break;case 1:if(ta(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){Ct(n,n.return,f)}else{var o=Pa(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){Ct(n,n.return,f)}}l&64&&Qf(n),l&512&&bs(n,n.return);break;case 3:if(ta(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Dc(t,e)}catch(f){Ct(n,n.return,f)}}break;case 27:e===null&&l&4&&Ff(n);case 26:case 5:ta(t,n),e===null&&l&4&&Jf(n),l&512&&bs(n,n.return);break;case 12:ta(t,n);break;case 13:ta(t,n),l&4&&ih(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=nm.bind(null,n),bm(t,n))));break;case 22:if(l=n.memoizedState!==null||mn,!l){e=e!==null&&e.memoizedState!==null||Wt,o=mn;var u=Wt;mn=l,(Wt=e)&&!u?ea(t,n,(n.subtreeFlags&8772)!==0):ta(t,n),mn=o,Wt=u}break;case 30:break;default:ta(t,n)}}function th(t){var e=t.alternate;e!==null&&(t.alternate=null,th(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ka(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var qt=null,Ge=!1;function _n(t,e,n){for(n=n.child;n!==null;)eh(t,e,n),n=n.sibling}function eh(t,e,n){if(Se&&typeof Se.onCommitFiberUnmount=="function")try{Se.onCommitFiberUnmount(ki,n)}catch{}switch(n.tag){case 26:Wt||Zi(n,e),_n(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Wt||Zi(n,e);var l=qt,o=Ge;oa(n.type)&&(qt=n.stateNode,Ge=!1),_n(t,e,n),Os(n.stateNode),qt=l,Ge=o;break;case 5:Wt||Zi(n,e);case 6:if(l=qt,o=Ge,qt=null,_n(t,e,n),qt=l,Ge=o,qt!==null)if(Ge)try{(qt.nodeType===9?qt.body:qt.nodeName==="HTML"?qt.ownerDocument.body:qt).removeChild(n.stateNode)}catch(u){Ct(n,e,u)}else try{qt.removeChild(n.stateNode)}catch(u){Ct(n,e,u)}break;case 18:qt!==null&&(Ge?(t=qt,Gh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),js(t)):Gh(qt,n.stateNode));break;case 4:l=qt,o=Ge,qt=n.stateNode.containerInfo,Ge=!0,_n(t,e,n),qt=l,Ge=o;break;case 0:case 11:case 14:case 15:Wt||$n(2,n,e),Wt||$n(4,n,e),_n(t,e,n);break;case 1:Wt||(Zi(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Kf(n,e,l)),_n(t,e,n);break;case 21:_n(t,e,n);break;case 22:Wt=(l=Wt)||n.memoizedState!==null,_n(t,e,n),Wt=l;break;default:_n(t,e,n)}}function ih(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{js(t)}catch(n){Ct(e,e.return,n)}}function Jd(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new If),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new If),e;default:throw Error(p(435,t.tag))}}function Ar(t,e){var n=Jd(t);e.forEach(function(l){var o=am.bind(null,t,l);n.has(l)||(n.add(l),l.then(o,o))})}function Ie(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var o=n[l],u=t,f=e,d=f;t:for(;d!==null;){switch(d.tag){case 27:if(oa(d.type)){qt=d.stateNode,Ge=!1;break t}break;case 5:qt=d.stateNode,Ge=!1;break t;case 3:case 4:qt=d.stateNode.containerInfo,Ge=!0;break t}d=d.return}if(qt===null)throw Error(p(160));eh(u,f,o),qt=null,Ge=!1,u=o.alternate,u!==null&&(u.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)nh(e,t),e=e.sibling}var Ti=null;function nh(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ie(e,t),$e(t),l&4&&($n(3,t,t.return),xs(3,t),$n(5,t,t.return));break;case 1:Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),l&64&&mn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var o=Ti;if(Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),l&4){var u=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(l){case"title":u=o.getElementsByTagName("title")[0],(!u||u[Mn]||u[le]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=o.createElement(l),o.head.insertBefore(u,o.querySelector("head > title"))),ye(u,l,n),u[le]=t,Ft(u),l=u;break t;case"link":var f=Fh("link","href",o).get(l+(n.href||""));if(f){for(var d=0;d<f.length;d++)if(u=f[d],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(d,1);break e}}u=o.createElement(l),ye(u,l,n),o.head.appendChild(u);break;case"meta":if(f=Fh("meta","content",o).get(l+(n.content||""))){for(d=0;d<f.length;d++)if(u=f[d],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(d,1);break e}}u=o.createElement(l),ye(u,l,n),o.head.appendChild(u);break;default:throw Error(p(468,l))}u[le]=t,Ft(u),l=u}t.stateNode=l}else Ih(o,t.type,t.stateNode);else t.stateNode=Wh(o,l,t.memoizedProps);else u!==l?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,l===null?Ih(o,t.type,t.stateNode):Wh(o,l,t.memoizedProps)):l===null&&t.stateNode!==null&&wr(t,t.memoizedProps,n.memoizedProps)}break;case 27:Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),n!==null&&l&4&&wr(t,t.memoizedProps,n.memoizedProps);break;case 5:if(Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),t.flags&32){o=t.stateNode;try{ai(o,"")}catch(A){Ct(t,t.return,A)}}l&4&&t.stateNode!=null&&(o=t.memoizedProps,wr(t,o,n!==null?n.memoizedProps:o)),l&1024&&(Lr=!0);break;case 6:if(Ie(e,t),$e(t),l&4){if(t.stateNode===null)throw Error(p(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(A){Ct(t,t.return,A)}}break;case 3:if(ru=null,o=Ti,Ti=ou(e.containerInfo),Ie(e,t),Ti=o,$e(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{js(e.containerInfo)}catch(A){Ct(t,t.return,A)}Lr&&(Lr=!1,ah(t));break;case 4:l=Ti,Ti=ou(t.stateNode.containerInfo),Ie(e,t),$e(t),Ti=l;break;case 12:Ie(e,t),$e(t);break;case 13:Ie(e,t),$e(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Br=je()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Ar(t,l)));break;case 22:o=t.memoizedState!==null;var v=n!==null&&n.memoizedState!==null,w=mn,Z=Wt;if(mn=w||o,Wt=Z||v,Ie(e,t),Wt=Z,mn=w,$e(t),l&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||v||mn||Wt||ka(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){v=n=e;try{if(u=v.stateNode,o)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{d=v.stateNode;var j=v.memoizedProps.style,z=j!=null&&j.hasOwnProperty("display")?j.display:null;d.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch(A){Ct(v,v.return,A)}}}else if(e.tag===6){if(n===null){v=e;try{v.stateNode.nodeValue=o?"":v.memoizedProps}catch(A){Ct(v,v.return,A)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,Ar(t,n))));break;case 19:Ie(e,t),$e(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Ar(t,l)));break;case 30:break;case 21:break;default:Ie(e,t),$e(t)}}function $e(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(Wf(l)){n=l;break}l=l.return}if(n==null)throw Error(p(160));switch(n.tag){case 27:var o=n.stateNode,u=Er(t);Ko(t,u,o);break;case 5:var f=n.stateNode;n.flags&32&&(ai(f,""),n.flags&=-33);var d=Er(t);Ko(t,d,f);break;case 3:case 4:var v=n.stateNode.containerInfo,w=Er(t);zr(t,w,v);break;default:throw Error(p(161))}}catch(Z){Ct(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function ah(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;ah(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ta(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)$f(t,e.alternate,e),e=e.sibling}function ka(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:$n(4,e,e.return),ka(e);break;case 1:Zi(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Kf(e,e.return,n),ka(e);break;case 27:Os(e.stateNode);case 26:case 5:Zi(e,e.return),ka(e);break;case 22:e.memoizedState===null&&ka(e);break;case 30:ka(e);break;default:ka(e)}t=t.sibling}}function ea(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,o=t,u=e,f=u.flags;switch(u.tag){case 0:case 11:case 15:ea(o,u,n),xs(4,u);break;case 1:if(ea(o,u,n),l=u,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(w){Ct(l,l.return,w)}if(l=u,o=l.updateQueue,o!==null){var d=l.stateNode;try{var v=o.shared.hiddenCallbacks;if(v!==null)for(o.shared.hiddenCallbacks=null,o=0;o<v.length;o++)Cc(v[o],d)}catch(w){Ct(l,l.return,w)}}n&&f&64&&Qf(u),bs(u,u.return);break;case 27:Ff(u);case 26:case 5:ea(o,u,n),n&&l===null&&f&4&&Jf(u),bs(u,u.return);break;case 12:ea(o,u,n);break;case 13:ea(o,u,n),n&&f&4&&ih(o,u);break;case 22:u.memoizedState===null&&ea(o,u,n),bs(u,u.return);break;case 30:break;default:ea(o,u,n)}e=e.sibling}}function Or(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&ss(n))}function Nr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ss(t))}function Ui(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)lh(t,e,n,l),e=e.sibling}function lh(t,e,n,l){var o=e.flags;switch(e.tag){case 0:case 11:case 15:Ui(t,e,n,l),o&2048&&xs(9,e);break;case 1:Ui(t,e,n,l);break;case 3:Ui(t,e,n,l),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ss(t)));break;case 12:if(o&2048){Ui(t,e,n,l),t=e.stateNode;try{var u=e.memoizedProps,f=u.id,d=u.onPostCommit;typeof d=="function"&&d(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(v){Ct(e,e.return,v)}}else Ui(t,e,n,l);break;case 13:Ui(t,e,n,l);break;case 23:break;case 22:u=e.stateNode,f=e.alternate,e.memoizedState!==null?u._visibility&2?Ui(t,e,n,l):Ss(t,e):u._visibility&2?Ui(t,e,n,l):(u._visibility|=2,Sl(t,e,n,l,(e.subtreeFlags&10256)!==0)),o&2048&&Or(f,e);break;case 24:Ui(t,e,n,l),o&2048&&Nr(e.alternate,e);break;default:Ui(t,e,n,l)}}function Sl(t,e,n,l,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,f=e,d=n,v=l,w=f.flags;switch(f.tag){case 0:case 11:case 15:Sl(u,f,d,v,o),xs(8,f);break;case 23:break;case 22:var Z=f.stateNode;f.memoizedState!==null?Z._visibility&2?Sl(u,f,d,v,o):Ss(u,f):(Z._visibility|=2,Sl(u,f,d,v,o)),o&&w&2048&&Or(f.alternate,f);break;case 24:Sl(u,f,d,v,o),o&&w&2048&&Nr(f.alternate,f);break;default:Sl(u,f,d,v,o)}e=e.sibling}}function Ss(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,o=l.flags;switch(l.tag){case 22:Ss(n,l),o&2048&&Or(l.alternate,l);break;case 24:Ss(n,l),o&2048&&Nr(l.alternate,l);break;default:Ss(n,l)}e=e.sibling}}var Ts=8192;function Tl(t){if(t.subtreeFlags&Ts)for(t=t.child;t!==null;)sh(t),t=t.sibling}function sh(t){switch(t.tag){case 26:Tl(t),t.flags&Ts&&t.memoizedState!==null&&Rm(Ti,t.memoizedState,t.memoizedProps);break;case 5:Tl(t);break;case 3:case 4:var e=Ti;Ti=ou(t.stateNode.containerInfo),Tl(t),Ti=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ts,Ts=16777216,Tl(t),Ts=e):Tl(t));break;default:Tl(t)}}function oh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ms(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];re=l,rh(l,t)}oh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)uh(t),t=t.sibling}function uh(t){switch(t.tag){case 0:case 11:case 15:Ms(t),t.flags&2048&&$n(9,t,t.return);break;case 3:Ms(t);break;case 12:Ms(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Jo(t)):Ms(t);break;default:Ms(t)}}function Jo(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];re=l,rh(l,t)}oh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:$n(8,e,e.return),Jo(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Jo(e));break;default:Jo(e)}t=t.sibling}}function rh(t,e){for(;re!==null;){var n=re;switch(n.tag){case 0:case 11:case 15:$n(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ss(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,re=l;else t:for(n=t;re!==null;){l=re;var o=l.sibling,u=l.return;if(th(l),l===n){re=null;break t}if(o!==null){o.return=u,re=o;break t}re=u}}}var Wd={getCacheForType:function(t){var e=Ee(te),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Fd=typeof WeakMap=="function"?WeakMap:Map,zt=0,Zt=null,pt=null,xt=0,Lt=0,ti=null,ia=!1,Ml=!1,Cr=!1,vn=0,Kt=0,na=0,Ga=0,Dr=0,hi=0,wl=0,ws=null,Ye=null,Rr=!1,Br=0,Wo=1/0,Fo=null,aa=null,ge=0,la=null,El=null,zl=0,Zr=0,Ur=null,ch=null,Es=0,jr=null;function ei(){if((zt&2)!==0&&xt!==0)return xt&-xt;if(D.T!==null){var t=ml;return t!==0?t:Vr()}return Fs()}function fh(){hi===0&&(hi=(xt&536870912)===0||ot?Ks():536870912);var t=fi.current;return t!==null&&(t.flags|=32),hi}function ii(t,e,n){(t===Zt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ll(t,0),sa(t,xt,hi,!1)),Sn(t,n),((zt&2)===0||t!==Zt)&&(t===Zt&&((zt&2)===0&&(Ga|=n),Kt===4&&sa(t,xt,hi,!1)),ji(t))}function hh(t,e,n){if((zt&6)!==0)throw Error(p(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||vi(t,e),o=l?tm(t,e):Pr(t,e,!0),u=l;do{if(o===0){Ml&&!l&&sa(t,e,0,!1);break}else{if(n=t.current.alternate,u&&!Id(n)){o=Pr(t,e,!1),u=!1;continue}if(o===2){if(u=e,t.errorRecoveryDisabledLanes&u)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var d=t;o=ws;var v=d.current.memoizedState.isDehydrated;if(v&&(Ll(d,f).flags|=256),f=Pr(d,f,!1),f!==2){if(Cr&&!v){d.errorRecoveryDisabledLanes|=u,Ga|=u,o=4;break t}u=Ye,Ye=o,u!==null&&(Ye===null?Ye=u:Ye.push.apply(Ye,u))}o=f}if(u=!1,o!==2)continue}}if(o===1){Ll(t,0),sa(t,e,0,!0);break}t:{switch(l=t,u=o,u){case 0:case 1:throw Error(p(345));case 4:if((e&4194048)!==e)break;case 6:sa(l,e,hi,!ia);break t;case 2:Ye=null;break;case 3:case 5:break;default:throw Error(p(329))}if((e&62914560)===e&&(o=Br+300-je(),10<o)){if(sa(l,e,hi,!ia),Xa(l,0,!0)!==0)break t;l.timeoutHandle=Ph(dh.bind(null,l,n,Ye,Fo,Rr,e,hi,Ga,wl,ia,u,2,-0,0),o);break t}dh(l,n,Ye,Fo,Rr,e,hi,Ga,wl,ia,u,0,-0,0)}}break}while(!0);ji(t)}function dh(t,e,n,l,o,u,f,d,v,w,Z,j,z,A){if(t.timeoutHandle=-1,j=e.subtreeFlags,(j&8192||(j&16785408)===16785408)&&(Ds={stylesheets:null,count:0,unsuspend:Dm},sh(e),j=Bm(),j!==null)){t.cancelPendingCommit=j(xh.bind(null,t,e,u,n,l,o,f,d,v,Z,1,z,A)),sa(t,u,f,!w);return}xh(t,e,u,n,l,o,f,d,v)}function Id(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var o=n[l],u=o.getSnapshot;o=o.value;try{if(!Ce(u(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function sa(t,e,n,l){e&=~Dr,e&=~Ga,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var o=e;0<o;){var u=31-Oe(o),f=1<<u;l[u]=-1,o&=~f}n!==0&&Js(t,n,e)}function Io(){return(zt&6)===0?(zs(0),!1):!0}function Hr(){if(pt!==null){if(Lt===0)var t=pt.return;else t=pt,bi=ci=null,ir(t),xl=null,ps=0,t=pt;for(;t!==null;)Xf(t.alternate,t),t=t.return;pt=null}}function Ll(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,vm(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Hr(),Zt=t,pt=n=ri(t.current,null),xt=e,Lt=0,ti=null,ia=!1,Ml=vi(t,e),Cr=!1,wl=hi=Dr=Ga=na=Kt=0,Ye=ws=null,Rr=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var o=31-Oe(l),u=1<<o;e|=t[o],l&=~u}return vn=e,Na(),n}function mh(t,e){dt=null,D.H=Ho,e===us||e===Ao?(e=Oc(),Lt=3):e===zc?(e=Oc(),Lt=4):Lt=e===Cf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ti=e,pt===null&&(Kt=1,Yo(t,Re(e,t.current)))}function _h(){var t=D.H;return D.H=Ho,t===null?Ho:t}function vh(){var t=D.A;return D.A=Wd,t}function qr(){Kt=4,ia||(xt&4194048)!==xt&&fi.current!==null||(Ml=!0),(na&134217727)===0&&(Ga&134217727)===0||Zt===null||sa(Zt,xt,hi,!1)}function Pr(t,e,n){var l=zt;zt|=2;var o=_h(),u=vh();(Zt!==t||xt!==e)&&(Fo=null,Ll(t,e)),e=!1;var f=Kt;t:do try{if(Lt!==0&&pt!==null){var d=pt,v=ti;switch(Lt){case 8:Hr(),f=6;break t;case 3:case 2:case 9:case 6:fi.current===null&&(e=!0);var w=Lt;if(Lt=0,ti=null,Al(t,d,v,w),n&&Ml){f=0;break t}break;default:w=Lt,Lt=0,ti=null,Al(t,d,v,w)}}$d(),f=Kt;break}catch(Z){mh(t,Z)}while(!0);return e&&t.shellSuspendCounter++,bi=ci=null,zt=l,D.H=o,D.A=u,pt===null&&(Zt=null,xt=0,Na()),f}function $d(){for(;pt!==null;)ph(pt)}function tm(t,e){var n=zt;zt|=2;var l=_h(),o=vh();Zt!==t||xt!==e?(Fo=null,Wo=je()+500,Ll(t,e)):Ml=vi(t,e);t:do try{if(Lt!==0&&pt!==null){e=pt;var u=ti;e:switch(Lt){case 1:Lt=0,ti=null,Al(t,e,u,1);break;case 2:case 9:if(Lc(u)){Lt=0,ti=null,gh(e);break}e=function(){Lt!==2&&Lt!==9||Zt!==t||(Lt=7),ji(t)},u.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:Lc(u)?(Lt=0,ti=null,gh(e)):(Lt=0,ti=null,Al(t,e,u,7));break;case 5:var f=null;switch(pt.tag){case 26:f=pt.memoizedState;case 5:case 27:var d=pt;if(!f||$h(f)){Lt=0,ti=null;var v=d.sibling;if(v!==null)pt=v;else{var w=d.return;w!==null?(pt=w,$o(w)):pt=null}break e}}Lt=0,ti=null,Al(t,e,u,5);break;case 6:Lt=0,ti=null,Al(t,e,u,6);break;case 8:Hr(),Kt=6;break t;default:throw Error(p(462))}}em();break}catch(Z){mh(t,Z)}while(!0);return bi=ci=null,D.H=l,D.A=o,zt=n,pt!==null?0:(Zt=null,xt=0,Na(),Kt)}function em(){for(;pt!==null&&!ks();)ph(pt)}function ph(t){var e=Yf(t.alternate,t,vn);t.memoizedProps=t.pendingProps,e===null?$o(t):pt=e}function gh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=jf(n,e,e.pendingProps,e.type,void 0,xt);break;case 11:e=jf(n,e,e.pendingProps,e.type.render,e.ref,xt);break;case 5:ir(e);default:Xf(n,e),e=pt=as(e,vn),e=Yf(n,e,vn)}t.memoizedProps=t.pendingProps,e===null?$o(t):pt=e}function Al(t,e,n,l){bi=ci=null,ir(e),xl=null,ps=0;var o=e.return;try{if(Yd(t,o,e,n,xt)){Kt=1,Yo(t,Re(n,t.current)),pt=null;return}}catch(u){if(o!==null)throw pt=o,u;Kt=1,Yo(t,Re(n,t.current)),pt=null;return}e.flags&32768?(ot||l===1?t=!0:Ml||(xt&536870912)!==0?t=!1:(ia=t=!0,(l===2||l===9||l===3||l===6)&&(l=fi.current,l!==null&&l.tag===13&&(l.flags|=16384))),yh(e,t)):$o(e)}function $o(t){var e=t;do{if((e.flags&32768)!==0){yh(e,ia);return}t=e.return;var n=Xd(e.alternate,e,vn);if(n!==null){pt=n;return}if(e=e.sibling,e!==null){pt=e;return}pt=e=t}while(e!==null);Kt===0&&(Kt=5)}function yh(t,e){do{var n=Qd(t.alternate,t);if(n!==null){n.flags&=32767,pt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){pt=t;return}pt=t=n}while(t!==null);Kt=6,pt=null}function xh(t,e,n,l,o,u,f,d,v){t.cancelPendingCommit=null;do tu();while(ge!==0);if((zt&6)!==0)throw Error(p(327));if(e!==null){if(e===t.current)throw Error(p(177));if(u=e.lanes|e.childLanes,u|=Fe,Tu(t,n,u,f,d,v),t===Zt&&(pt=Zt=null,xt=0),El=e,la=t,zl=n,Zr=u,Ur=o,ch=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,lm(xn,function(){return wh(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=D.T,D.T=null,o=Q.p,Q.p=2,f=zt,zt|=4;try{Kd(t,e,n)}finally{zt=f,Q.p=o,D.T=l}}ge=1,bh(),Sh(),Th()}}function bh(){if(ge===1){ge=0;var t=la,e=El,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=zt;zt|=4;try{nh(e,t);var u=$r,f=La(t.containerInfo),d=u.focusedElem,v=u.selectionRange;if(f!==d&&d&&d.ownerDocument&&sl(d.ownerDocument.documentElement,d)){if(v!==null&&Aa(d)){var w=v.start,Z=v.end;if(Z===void 0&&(Z=w),"selectionStart"in d)d.selectionStart=w,d.selectionEnd=Math.min(Z,d.value.length);else{var j=d.ownerDocument||document,z=j&&j.defaultView||window;if(z.getSelection){var A=z.getSelection(),st=d.textContent.length,at=Math.min(v.start,st),Nt=v.end===void 0?at:Math.min(v.end,st);!A.extend&&at>Nt&&(f=Nt,Nt=at,at=f);var S=is(d,at),y=is(d,Nt);if(S&&y&&(A.rangeCount!==1||A.anchorNode!==S.node||A.anchorOffset!==S.offset||A.focusNode!==y.node||A.focusOffset!==y.offset)){var M=j.createRange();M.setStart(S.node,S.offset),A.removeAllRanges(),at>Nt?(A.addRange(M),A.extend(y.node,y.offset)):(M.setEnd(y.node,y.offset),A.addRange(M))}}}}for(j=[],A=d;A=A.parentNode;)A.nodeType===1&&j.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<j.length;d++){var U=j[d];U.element.scrollLeft=U.left,U.element.scrollTop=U.top}}hu=!!Ir,$r=Ir=null}finally{zt=o,Q.p=l,D.T=n}}t.current=e,ge=2}}function Sh(){if(ge===2){ge=0;var t=la,e=El,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=zt;zt|=4;try{$f(t,e.alternate,e)}finally{zt=o,Q.p=l,D.T=n}}ge=3}}function Th(){if(ge===4||ge===3){ge=0,Gs();var t=la,e=El,n=zl,l=ch;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ge=5:(ge=0,El=la=null,Mh(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(aa=null),ql(n),e=e.stateNode,Se&&typeof Se.onCommitFiberRoot=="function")try{Se.onCommitFiberRoot(ki,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=D.T,o=Q.p,Q.p=2,D.T=null;try{for(var u=t.onRecoverableError,f=0;f<l.length;f++){var d=l[f];u(d.value,{componentStack:d.stack})}}finally{D.T=e,Q.p=o}}(zl&3)!==0&&tu(),ji(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===jr?Es++:(Es=0,jr=t):Es=0,zs(0)}}function Mh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ss(e)))}function tu(t){return bh(),Sh(),Th(),wh()}function wh(){if(ge!==5)return!1;var t=la,e=Zr;Zr=0;var n=ql(zl),l=D.T,o=Q.p;try{Q.p=32>n?32:n,D.T=null,n=Ur,Ur=null;var u=la,f=zl;if(ge=0,El=la=null,zl=0,(zt&6)!==0)throw Error(p(331));var d=zt;if(zt|=4,uh(u.current),lh(u,u.current,f,n),zt=d,zs(0,!1),Se&&typeof Se.onPostCommitFiberRoot=="function")try{Se.onPostCommitFiberRoot(ki,u)}catch{}return!0}finally{Q.p=o,D.T=l,Mh(t,e)}}function Eh(t,e,n){e=Re(n,e),e=vr(t.stateNode,e,2),t=Jn(t,e,2),t!==null&&(Sn(t,2),ji(t))}function Ct(t,e,n){if(t.tag===3)Eh(t,t,n);else for(;e!==null;){if(e.tag===3){Eh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(aa===null||!aa.has(l))){t=Re(n,t),n=Of(2),l=Jn(e,n,2),l!==null&&(Nf(n,l,e,t),Sn(l,2),ji(l));break}}e=e.return}}function kr(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new Fd;var o=new Set;l.set(e,o)}else o=l.get(e),o===void 0&&(o=new Set,l.set(e,o));o.has(n)||(Cr=!0,o.add(n),t=im.bind(null,t,e,n),e.then(t,t))}function im(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Zt===t&&(xt&n)===n&&(Kt===4||Kt===3&&(xt&62914560)===xt&&300>je()-Br?(zt&2)===0&&Ll(t,0):Dr|=n,wl===xt&&(wl=0)),ji(t)}function zh(t,e){e===0&&(e=jl()),t=Gn(t,e),t!==null&&(Sn(t,e),ji(t))}function nm(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),zh(t,n)}function am(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(p(314))}l!==null&&l.delete(e),zh(t,n)}function lm(t,e){return _a(t,e)}var eu=null,Ol=null,Gr=!1,iu=!1,Yr=!1,Ya=0;function ji(t){t!==Ol&&t.next===null&&(Ol===null?eu=Ol=t:Ol=Ol.next=t),iu=!0,Gr||(Gr=!0,om())}function zs(t,e){if(!Yr&&iu){Yr=!0;do for(var n=!1,l=eu;l!==null;){if(t!==0){var o=l.pendingLanes;if(o===0)var u=0;else{var f=l.suspendedLanes,d=l.pingedLanes;u=(1<<31-Oe(42|t)+1)-1,u&=o&~(f&~d),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,Nh(l,u))}else u=xt,u=Xa(l,l===Zt?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||vi(l,u)||(n=!0,Nh(l,u));l=l.next}while(n);Yr=!1}}function sm(){Lh()}function Lh(){iu=Gr=!1;var t=0;Ya!==0&&(_m()&&(t=Ya),Ya=0);for(var e=je(),n=null,l=eu;l!==null;){var o=l.next,u=Ah(l,e);u===0?(l.next=null,n===null?eu=o:n.next=o,o===null&&(Ol=n)):(n=l,(t!==0||(u&3)!==0)&&(iu=!0)),l=o}zs(t)}function Ah(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,o=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var f=31-Oe(u),d=1<<f,v=o[f];v===-1?((d&n)===0||(d&l)!==0)&&(o[f]=Su(d,e)):v<=e&&(t.expiredLanes|=d),u&=~d}if(e=Zt,n=xt,n=Xa(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Bl(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||vi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Bl(l),ql(n)){case 2:case 8:n=Zl;break;case 32:n=xn;break;case 268435456:n=Ul;break;default:n=xn}return l=Oh.bind(null,t),n=_a(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Bl(l),t.callbackPriority=2,t.callbackNode=null,2}function Oh(t,e){if(ge!==0&&ge!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(tu()&&t.callbackNode!==n)return null;var l=xt;return l=Xa(t,t===Zt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(hh(t,l,e),Ah(t,je()),t.callbackNode!=null&&t.callbackNode===n?Oh.bind(null,t):null)}function Nh(t,e){if(tu())return null;hh(t,e,!0)}function om(){pm(function(){(zt&6)!==0?_a(Ys,sm):Lh()})}function Vr(){return Ya===0&&(Ya=Ks()),Ya}function Ch(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:On(""+t)}function Dh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function um(t,e,n,l,o){if(e==="submit"&&n&&n.stateNode===o){var u=Ch((o[Te]||null).action),f=l.submitter;f&&(e=(e=f[Te]||null)?Ch(e.formAction):f.getAttribute("formAction"),e!==null&&(u=e,f=null));var d=new Ta("action","action",null,l,o);t.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ya!==0){var v=f?Dh(o,f):new FormData(o);fr(n,{pending:!0,data:v,method:o.method,action:u},null,v)}}else typeof u=="function"&&(d.preventDefault(),v=f?Dh(o,f):new FormData(o),fr(n,{pending:!0,data:v,method:o.method,action:u},u,v))},currentTarget:o}]})}}for(var Xr=0;Xr<ln.length;Xr++){var Qr=ln[Xr],rm=Qr.toLowerCase(),cm=Qr[0].toUpperCase()+Qr.slice(1);We(rm,"on"+cm)}We(xo,"onAnimationEnd"),We(Je,"onAnimationIteration"),We(Oa,"onAnimationStart"),We("dblclick","onDoubleClick"),We("focusin","onFocus"),We("focusout","onBlur"),We(qu,"onTransitionRun"),We(cl,"onTransitionStart"),We(Pu,"onTransitionCancel"),We(ns,"onTransitionEnd"),Xi("onMouseEnter",["mouseout","mouseover"]),Xi("onMouseLeave",["mouseout","mouseover"]),Xi("onPointerEnter",["pointerout","pointerover"]),Xi("onPointerLeave",["pointerout","pointerover"]),Vi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Vi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Vi("onBeforeInput",["compositionend","keypress","textInput","paste"]),Vi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Vi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Vi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ls="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ls));function Rh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],o=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var f=l.length-1;0<=f;f--){var d=l[f],v=d.instance,w=d.currentTarget;if(d=d.listener,v!==u&&o.isPropagationStopped())break t;u=d,o.currentTarget=w;try{u(o)}catch(Z){Go(Z)}o.currentTarget=null,u=v}else for(f=0;f<l.length;f++){if(d=l[f],v=d.instance,w=d.currentTarget,d=d.listener,v!==u&&o.isPropagationStopped())break t;u=d,o.currentTarget=w;try{u(o)}catch(Z){Go(Z)}o.currentTarget=null,u=v}}}}function gt(t,e){var n=e[He];n===void 0&&(n=e[He]=new Set);var l=t+"__bubble";n.has(l)||(Bh(e,t,2,!1),n.add(l))}function Kr(t,e,n){var l=0;e&&(l|=4),Bh(n,t,l,e)}var nu="_reactListening"+Math.random().toString(36).slice(2);function Jr(t){if(!t[nu]){t[nu]=!0,$s.forEach(function(n){n!=="selectionchange"&&(fm.has(n)||Kr(n,!1,t),Kr(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[nu]||(e[nu]=!0,Kr("selectionchange",!1,e))}}function Bh(t,e,n,l){switch(ld(e)){case 2:var o=jm;break;case 8:o=Hm;break;default:o=rc}n=o.bind(null,e,n,t),o=void 0,!Sa||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),l?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Wr(t,e,n,l,o){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var d=l.stateNode.containerInfo;if(d===o)break;if(f===4)for(f=l.return;f!==null;){var v=f.tag;if((v===3||v===4)&&f.stateNode.containerInfo===o)return;f=f.return}for(;d!==null;){if(f=zi(d),f===null)return;if(v=f.tag,v===5||v===6||v===26||v===27){l=u=f;continue t}d=d.parentNode}}l=l.return}Ve(function(){var w=u,Z=Nn(n),j=[];t:{var z=bo.get(t);if(z!==void 0){var A=Ta,st=t;switch(t){case"keypress":if(Qt(n)===0)break t;case"keydown":case"keyup":A=Cu;break;case"focusin":st="focus",A=Ql;break;case"focusout":st="blur",A=Ql;break;case"beforeblur":case"afterblur":A=Ql;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=Rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=zu;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Ru;break;case xo:case Je:case Oa:A=Lu;break;case ns:A=Bu;break;case"scroll":case"scrollend":A=Eu;break;case"wheel":A=co;break;case"copy":case"cut":case"paste":A=Kl;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=Wl;break;case"toggle":case"beforetoggle":A=tn}var at=(e&4)!==0,Nt=!at&&(t==="scroll"||t==="scrollend"),S=at?z!==null?z+"Capture":null:z;at=[];for(var y=w,M;y!==null;){var U=y;if(M=U.stateNode,U=U.tag,U!==5&&U!==26&&U!==27||M===null||S===null||(U=St(y,S),U!=null&&at.push(As(y,U,M))),Nt)break;y=y.return}0<at.length&&(z=new A(z,st,null,n,Z),j.push({event:z,listeners:at}))}}if((e&7)===0){t:{if(z=t==="mouseover"||t==="pointerover",A=t==="mouseout"||t==="pointerout",z&&n!==ba&&(st=n.relatedTarget||n.fromElement)&&(zi(st)||st[Tn]))break t;if((A||z)&&(z=Z.window===Z?Z:(z=Z.ownerDocument)?z.defaultView||z.parentWindow:window,A?(st=n.relatedTarget||n.toElement,A=w,st=st?zi(st):null,st!==null&&(Nt=Y(st),at=st.tag,st!==Nt||at!==5&&at!==27&&at!==6)&&(st=null)):(A=null,st=w),A!==st)){if(at=Rn,U="onMouseLeave",S="onMouseEnter",y="mouse",(t==="pointerout"||t==="pointerover")&&(at=Wl,U="onPointerLeave",S="onPointerEnter",y="pointer"),Nt=A==null?z:ni(A),M=st==null?z:ni(st),z=new at(U,y+"leave",A,n,Z),z.target=Nt,z.relatedTarget=M,U=null,zi(Z)===w&&(at=new at(S,y+"enter",st,n,Z),at.target=M,at.relatedTarget=Nt,U=at),Nt=U,A&&st)e:{for(at=A,S=st,y=0,M=at;M;M=Nl(M))y++;for(M=0,U=S;U;U=Nl(U))M++;for(;0<y-M;)at=Nl(at),y--;for(;0<M-y;)S=Nl(S),M--;for(;y--;){if(at===S||S!==null&&at===S.alternate)break e;at=Nl(at),S=Nl(S)}at=null}else at=null;A!==null&&Zh(j,z,A,at,!1),st!==null&&Nt!==null&&Zh(j,Nt,st,at,!0)}}t:{if(z=w?ni(w):window,A=z.nodeName&&z.nodeName.toLowerCase(),A==="select"||A==="input"&&z.type==="file")var F=Un;else if(nn(z))if($l)F=Hu;else{F=ju;var vt=es}else A=z.nodeName,!A||A.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?w&&xa(w.elementType)&&(F=Un):F=xi;if(F&&(F=F(t,w))){vo(j,F,n,Z);break t}vt&&vt(t,z,w),t==="focusout"&&w&&z.type==="number"&&w.memoizedProps.value!=null&&Ln(z,"number",z.value)}switch(vt=w?ni(w):window,t){case"focusin":(nn(vt)||vt.contentEditable==="true")&&(Ke=vt,qn=w,an=null);break;case"focusout":an=qn=Ke=null;break;case"mousedown":ul=!0;break;case"contextmenu":case"mouseup":case"dragend":ul=!1,go(j,n,Z);break;case"selectionchange":if(ol)break;case"keydown":case"keyup":go(j,n,Z)}var it;if(en)t:{switch(t){case"compositionstart":var lt="onCompositionStart";break t;case"compositionend":lt="onCompositionEnd";break t;case"compositionupdate":lt="onCompositionUpdate";break t}lt=void 0}else Bn?al(t,n)&&(lt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(lt="onCompositionStart");lt&&(Il&&n.locale!=="ko"&&(Bn||lt!=="onCompositionStart"?lt==="onCompositionEnd"&&Bn&&(it=Dn()):(si=Z,gi="value"in si?si.value:si.textContent,Bn=!0)),vt=au(w,lt),0<vt.length&&(lt=new Xe(lt,t,null,n,Z),j.push({event:lt,listeners:vt}),it?lt.data=it:(it=mo(n),it!==null&&(lt.data=it)))),(it=fo?_o(t,n):Uu(t,n))&&(lt=au(w,"onBeforeInput"),0<lt.length&&(vt=new Xe("onBeforeInput","beforeinput",null,n,Z),j.push({event:vt,listeners:lt}),vt.data=it)),um(j,t,w,n,Z)}Rh(j,e)})}function As(t,e,n){return{instance:t,listener:e,currentTarget:n}}function au(t,e){for(var n=e+"Capture",l=[];t!==null;){var o=t,u=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||u===null||(o=St(t,n),o!=null&&l.unshift(As(t,o,u)),o=St(t,e),o!=null&&l.push(As(t,o,u))),t.tag===3)return l;t=t.return}return[]}function Nl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Zh(t,e,n,l,o){for(var u=e._reactName,f=[];n!==null&&n!==l;){var d=n,v=d.alternate,w=d.stateNode;if(d=d.tag,v!==null&&v===l)break;d!==5&&d!==26&&d!==27||w===null||(v=w,o?(w=St(n,u),w!=null&&f.unshift(As(n,w,v))):o||(w=St(n,u),w!=null&&f.push(As(n,w,v)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var hm=/\r\n?/g,dm=/\u0000|\uFFFD/g;function Uh(t){return(typeof t=="string"?t:""+t).replace(hm,`
`).replace(dm,"")}function jh(t,e){return e=Uh(e),Uh(t)===e}function lu(){}function Ot(t,e,n,l,o,u){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ai(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ai(t,""+l);break;case"className":Wa(t,"class",l);break;case"tabIndex":Wa(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Wa(t,n,l);break;case"style":An(t,l,u);break;case"data":if(e!=="object"){Wa(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=On(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(e!=="input"&&Ot(t,e,"name",o.name,o,null),Ot(t,e,"formEncType",o.formEncType,o,null),Ot(t,e,"formMethod",o.formMethod,o,null),Ot(t,e,"formTarget",o.formTarget,o,null)):(Ot(t,e,"encType",o.encType,o,null),Ot(t,e,"method",o.method,o,null),Ot(t,e,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=On(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=lu);break;case"onScroll":l!=null&&gt("scroll",t);break;case"onScrollEnd":l!=null&&gt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(p(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(p(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=On(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":gt("beforetoggle",t),gt("toggle",t),Ja(t,"popover",l);break;case"xlinkActuate":pi(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":pi(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":pi(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":pi(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":pi(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":pi(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":pi(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":pi(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":pi(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Ja(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Yl.get(n)||n,Ja(t,n,l))}}function Fr(t,e,n,l,o,u){switch(n){case"style":An(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(p(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(p(60));t.innerHTML=n}}break;case"children":typeof l=="string"?ai(t,l):(typeof l=="number"||typeof l=="bigint")&&ai(t,""+l);break;case"onScroll":l!=null&&gt("scroll",t);break;case"onScrollEnd":l!=null&&gt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=lu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!to.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),u=t[Te]||null,u=u!=null?u[n]:null,typeof u=="function"&&t.removeEventListener(e,u,o),typeof l=="function")){typeof u!="function"&&u!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,o);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Ja(t,n,l)}}}function ye(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":gt("error",t),gt("load",t);var l=!1,o=!1,u;for(u in n)if(n.hasOwnProperty(u)){var f=n[u];if(f!=null)switch(u){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(p(137,e));default:Ot(t,e,u,f,n,null)}}o&&Ot(t,e,"srcSet",n.srcSet,n,null),l&&Ot(t,e,"src",n.src,n,null);return;case"input":gt("invalid",t);var d=u=f=o=null,v=null,w=null;for(l in n)if(n.hasOwnProperty(l)){var Z=n[l];if(Z!=null)switch(l){case"name":o=Z;break;case"type":f=Z;break;case"checked":v=Z;break;case"defaultChecked":w=Z;break;case"value":u=Z;break;case"defaultValue":d=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(p(137,e));break;default:Ot(t,e,l,Z,n,null)}}io(t,u,d,v,w,f,o,!1),zn(t);return;case"select":gt("invalid",t),l=f=u=null;for(o in n)if(n.hasOwnProperty(o)&&(d=n[o],d!=null))switch(o){case"value":u=d;break;case"defaultValue":f=d;break;case"multiple":l=d;default:Ot(t,e,o,d,n,null)}e=u,n=f,t.multiple=!!l,e!=null?qe(t,!!l,e,!1):n!=null&&qe(t,!!l,n,!0);return;case"textarea":gt("invalid",t),u=o=l=null;for(f in n)if(n.hasOwnProperty(f)&&(d=n[f],d!=null))switch(f){case"value":l=d;break;case"defaultValue":o=d;break;case"children":u=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(p(91));break;default:Ot(t,e,f,d,n,null)}Li(t,l,o,u),zn(t);return;case"option":for(v in n)if(n.hasOwnProperty(v)&&(l=n[v],l!=null))switch(v){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ot(t,e,v,l,n,null)}return;case"dialog":gt("beforetoggle",t),gt("toggle",t),gt("cancel",t),gt("close",t);break;case"iframe":case"object":gt("load",t);break;case"video":case"audio":for(l=0;l<Ls.length;l++)gt(Ls[l],t);break;case"image":gt("error",t),gt("load",t);break;case"details":gt("toggle",t);break;case"embed":case"source":case"link":gt("error",t),gt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(w in n)if(n.hasOwnProperty(w)&&(l=n[w],l!=null))switch(w){case"children":case"dangerouslySetInnerHTML":throw Error(p(137,e));default:Ot(t,e,w,l,n,null)}return;default:if(xa(e)){for(Z in n)n.hasOwnProperty(Z)&&(l=n[Z],l!==void 0&&Fr(t,e,Z,l,n,void 0));return}}for(d in n)n.hasOwnProperty(d)&&(l=n[d],l!=null&&Ot(t,e,d,l,n,null))}function mm(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,u=null,f=null,d=null,v=null,w=null,Z=null;for(A in n){var j=n[A];if(n.hasOwnProperty(A)&&j!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":v=j;default:l.hasOwnProperty(A)||Ot(t,e,A,null,l,j)}}for(var z in l){var A=l[z];if(j=n[z],l.hasOwnProperty(z)&&(A!=null||j!=null))switch(z){case"type":u=A;break;case"name":o=A;break;case"checked":w=A;break;case"defaultChecked":Z=A;break;case"value":f=A;break;case"defaultValue":d=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(p(137,e));break;default:A!==j&&Ot(t,e,z,A,l,j)}}Me(t,f,d,v,w,Z,u,o);return;case"select":A=f=d=z=null;for(u in n)if(v=n[u],n.hasOwnProperty(u)&&v!=null)switch(u){case"value":break;case"multiple":A=v;default:l.hasOwnProperty(u)||Ot(t,e,u,null,l,v)}for(o in l)if(u=l[o],v=n[o],l.hasOwnProperty(o)&&(u!=null||v!=null))switch(o){case"value":z=u;break;case"defaultValue":d=u;break;case"multiple":f=u;default:u!==v&&Ot(t,e,o,u,l,v)}e=d,n=f,l=A,z!=null?qe(t,!!n,z,!1):!!l!=!!n&&(e!=null?qe(t,!!n,e,!0):qe(t,!!n,n?[]:"",!1));return;case"textarea":A=z=null;for(d in n)if(o=n[d],n.hasOwnProperty(d)&&o!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Ot(t,e,d,null,l,o)}for(f in l)if(o=l[f],u=n[f],l.hasOwnProperty(f)&&(o!=null||u!=null))switch(f){case"value":z=o;break;case"defaultValue":A=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(p(91));break;default:o!==u&&Ot(t,e,f,o,l,u)}Gt(t,z,A);return;case"option":for(var st in n)if(z=n[st],n.hasOwnProperty(st)&&z!=null&&!l.hasOwnProperty(st))switch(st){case"selected":t.selected=!1;break;default:Ot(t,e,st,null,l,z)}for(v in l)if(z=l[v],A=n[v],l.hasOwnProperty(v)&&z!==A&&(z!=null||A!=null))switch(v){case"selected":t.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Ot(t,e,v,z,l,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var at in n)z=n[at],n.hasOwnProperty(at)&&z!=null&&!l.hasOwnProperty(at)&&Ot(t,e,at,null,l,z);for(w in l)if(z=l[w],A=n[w],l.hasOwnProperty(w)&&z!==A&&(z!=null||A!=null))switch(w){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(p(137,e));break;default:Ot(t,e,w,z,l,A)}return;default:if(xa(e)){for(var Nt in n)z=n[Nt],n.hasOwnProperty(Nt)&&z!==void 0&&!l.hasOwnProperty(Nt)&&Fr(t,e,Nt,void 0,l,z);for(Z in l)z=l[Z],A=n[Z],!l.hasOwnProperty(Z)||z===A||z===void 0&&A===void 0||Fr(t,e,Z,z,l,A);return}}for(var S in n)z=n[S],n.hasOwnProperty(S)&&z!=null&&!l.hasOwnProperty(S)&&Ot(t,e,S,null,l,z);for(j in l)z=l[j],A=n[j],!l.hasOwnProperty(j)||z===A||z==null&&A==null||Ot(t,e,j,z,l,A)}var Ir=null,$r=null;function su(t){return t.nodeType===9?t:t.ownerDocument}function Hh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function qh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function tc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ec=null;function _m(){var t=window.event;return t&&t.type==="popstate"?t===ec?!1:(ec=t,!0):(ec=null,!1)}var Ph=typeof setTimeout=="function"?setTimeout:void 0,vm=typeof clearTimeout=="function"?clearTimeout:void 0,kh=typeof Promise=="function"?Promise:void 0,pm=typeof queueMicrotask=="function"?queueMicrotask:typeof kh<"u"?function(t){return kh.resolve(null).then(t).catch(gm)}:Ph;function gm(t){setTimeout(function(){throw t})}function oa(t){return t==="head"}function Gh(t,e){var n=e,l=0,o=0;do{var u=n.nextSibling;if(t.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<l&&8>l){n=l;var f=t.ownerDocument;if(n&1&&Os(f.documentElement),n&2&&Os(f.body),n&4)for(n=f.head,Os(n),f=n.firstChild;f;){var d=f.nextSibling,v=f.nodeName;f[Mn]||v==="SCRIPT"||v==="STYLE"||v==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=d}}if(o===0){t.removeChild(u),js(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:l=n.charCodeAt(0)-48;else l=0;n=u}while(n);js(e)}function ic(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ic(n),Ka(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function ym(t,e,n,l){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Mn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Mi(t.nextSibling),t===null)break}return null}function xm(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Mi(t.nextSibling),t===null))return null;return t}function nc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function bm(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Mi(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ac=null;function Yh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Vh(t,e,n){switch(e=su(n),t){case"html":if(t=e.documentElement,!t)throw Error(p(452));return t;case"head":if(t=e.head,!t)throw Error(p(453));return t;case"body":if(t=e.body,!t)throw Error(p(454));return t;default:throw Error(p(451))}}function Os(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ka(t)}var di=new Map,Xh=new Set;function ou(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var pn=Q.d;Q.d={f:Sm,r:Tm,D:Mm,C:wm,L:Em,m:zm,X:Am,S:Lm,M:Om};function Sm(){var t=pn.f(),e=Io();return t||e}function Tm(t){var e=Gi(t);e!==null&&e.tag===5&&e.type==="form"?hf(e):pn.r(t)}var Cl=typeof document>"u"?null:document;function Qh(t,e,n){var l=Cl;if(l&&typeof e=="string"&&e){var o=_e(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Xh.has(o)||(Xh.add(o),t={rel:t,crossOrigin:n,href:e},l.querySelector(o)===null&&(e=l.createElement("link"),ye(e,"link",t),Ft(e),l.head.appendChild(e)))}}function Mm(t){pn.D(t),Qh("dns-prefetch",t,null)}function wm(t,e){pn.C(t,e),Qh("preconnect",t,e)}function Em(t,e,n){pn.L(t,e,n);var l=Cl;if(l&&t&&e){var o='link[rel="preload"][as="'+_e(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+_e(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+_e(n.imageSizes)+'"]')):o+='[href="'+_e(t)+'"]';var u=o;switch(e){case"style":u=Dl(t);break;case"script":u=Rl(t)}di.has(u)||(t=q({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),di.set(u,t),l.querySelector(o)!==null||e==="style"&&l.querySelector(Ns(u))||e==="script"&&l.querySelector(Cs(u))||(e=l.createElement("link"),ye(e,"link",t),Ft(e),l.head.appendChild(e)))}}function zm(t,e){pn.m(t,e);var n=Cl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+_e(l)+'"][href="'+_e(t)+'"]',u=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Rl(t)}if(!di.has(u)&&(t=q({rel:"modulepreload",href:t},e),di.set(u,t),n.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Cs(u)))return}l=n.createElement("link"),ye(l,"link",t),Ft(l),n.head.appendChild(l)}}}function Lm(t,e,n){pn.S(t,e,n);var l=Cl;if(l&&t){var o=Yi(l).hoistableStyles,u=Dl(t);e=e||"default";var f=o.get(u);if(!f){var d={loading:0,preload:null};if(f=l.querySelector(Ns(u)))d.loading=5;else{t=q({rel:"stylesheet",href:t,"data-precedence":e},n),(n=di.get(u))&&lc(t,n);var v=f=l.createElement("link");Ft(v),ye(v,"link",t),v._p=new Promise(function(w,Z){v.onload=w,v.onerror=Z}),v.addEventListener("load",function(){d.loading|=1}),v.addEventListener("error",function(){d.loading|=2}),d.loading|=4,uu(f,e,l)}f={type:"stylesheet",instance:f,count:1,state:d},o.set(u,f)}}}function Am(t,e){pn.X(t,e);var n=Cl;if(n&&t){var l=Yi(n).hoistableScripts,o=Rl(t),u=l.get(o);u||(u=n.querySelector(Cs(o)),u||(t=q({src:t,async:!0},e),(e=di.get(o))&&sc(t,e),u=n.createElement("script"),Ft(u),ye(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Om(t,e){pn.M(t,e);var n=Cl;if(n&&t){var l=Yi(n).hoistableScripts,o=Rl(t),u=l.get(o);u||(u=n.querySelector(Cs(o)),u||(t=q({src:t,async:!0,type:"module"},e),(e=di.get(o))&&sc(t,e),u=n.createElement("script"),Ft(u),ye(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Kh(t,e,n,l){var o=(o=X.current)?ou(o):null;if(!o)throw Error(p(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Dl(n.href),n=Yi(o).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Dl(n.href);var u=Yi(o).hoistableStyles,f=u.get(t);if(f||(o=o.ownerDocument||o,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,f),(u=o.querySelector(Ns(t)))&&!u._p&&(f.instance=u,f.state.loading=5),di.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},di.set(t,n),u||Nm(o,t,n,f.state))),e&&l===null)throw Error(p(528,""));return f}if(e&&l!==null)throw Error(p(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Rl(n),n=Yi(o).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(p(444,t))}}function Dl(t){return'href="'+_e(t)+'"'}function Ns(t){return'link[rel="stylesheet"]['+t+"]"}function Jh(t){return q({},t,{"data-precedence":t.precedence,precedence:null})}function Nm(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),ye(e,"link",n),Ft(e),t.head.appendChild(e))}function Rl(t){return'[src="'+_e(t)+'"]'}function Cs(t){return"script[async]"+t}function Wh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+_e(n.href)+'"]');if(l)return e.instance=l,Ft(l),l;var o=q({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Ft(l),ye(l,"style",o),uu(l,n.precedence,t),e.instance=l;case"stylesheet":o=Dl(n.href);var u=t.querySelector(Ns(o));if(u)return e.state.loading|=4,e.instance=u,Ft(u),u;l=Jh(n),(o=di.get(o))&&lc(l,o),u=(t.ownerDocument||t).createElement("link"),Ft(u);var f=u;return f._p=new Promise(function(d,v){f.onload=d,f.onerror=v}),ye(u,"link",l),e.state.loading|=4,uu(u,n.precedence,t),e.instance=u;case"script":return u=Rl(n.src),(o=t.querySelector(Cs(u)))?(e.instance=o,Ft(o),o):(l=n,(o=di.get(u))&&(l=q({},n),sc(l,o)),t=t.ownerDocument||t,o=t.createElement("script"),Ft(o),ye(o,"link",l),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(p(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,uu(l,n.precedence,t));return e.instance}function uu(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,u=o,f=0;f<l.length;f++){var d=l[f];if(d.dataset.precedence===e)u=d;else if(u!==o)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function lc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function sc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ru=null;function Fh(t,e,n){if(ru===null){var l=new Map,o=ru=new Map;o.set(n,l)}else o=ru,l=o.get(n),l||(l=new Map,o.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var u=n[o];if(!(u[Mn]||u[le]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(e)||"";f=t+f;var d=l.get(f);d?d.push(u):l.set(f,[u])}}return l}function Ih(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Cm(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function $h(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ds=null;function Dm(){}function Rm(t,e,n){if(Ds===null)throw Error(p(475));var l=Ds;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Dl(n.href),u=t.querySelector(Ns(o));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=cu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Ft(u);return}u=t.ownerDocument||t,n=Jh(n),(o=di.get(o))&&lc(n,o),u=u.createElement("link"),Ft(u);var f=u;f._p=new Promise(function(d,v){f.onload=d,f.onerror=v}),ye(u,"link",n),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=cu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Bm(){if(Ds===null)throw Error(p(475));var t=Ds;return t.stylesheets&&t.count===0&&oc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&oc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function cu(){if(this.count--,this.count===0){if(this.stylesheets)oc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var fu=null;function oc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,fu=new Map,e.forEach(Zm,t),fu=null,cu.call(t))}function Zm(t,e){if(!(e.state.loading&4)){var n=fu.get(t);if(n)var l=n.get(null);else{n=new Map,fu.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<o.length;u++){var f=o[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}o=e.instance,f=o.getAttribute("data-precedence"),u=n.get(f)||l,u===l&&n.set(null,o),n.set(f,o),this.count++,l=cu.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),u?u.parentNode.insertBefore(o,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var Rs={$$typeof:Mt,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function Um(t,e,n,l,o,u,f,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Qa(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qa(0),this.hiddenUpdates=Qa(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function td(t,e,n,l,o,u,f,d,v,w,Z,j){return t=new Um(t,e,n,f,d,v,w,j),e=1,u===!0&&(e|=24),u=Ze(3,null,null,e),t.current=u,u.stateNode=t,e=ku(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:n,cache:e},Xu(u),t}function ed(t){return t?(t=un,t):un}function id(t,e,n,l,o,u){o=ed(o),l.context===null?l.context=o:l.pendingContext=o,l=Kn(e),l.payload={element:n},u=u===void 0?null:u,u!==null&&(l.callback=u),n=Jn(t,l,e),n!==null&&(ii(n,t,e),cs(n,t,e))}function nd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function uc(t,e){nd(t,e),(t=t.alternate)&&nd(t,e)}function ad(t){if(t.tag===13){var e=Gn(t,67108864);e!==null&&ii(e,t,67108864),uc(t,67108864)}}var hu=!0;function jm(t,e,n,l){var o=D.T;D.T=null;var u=Q.p;try{Q.p=2,rc(t,e,n,l)}finally{Q.p=u,D.T=o}}function Hm(t,e,n,l){var o=D.T;D.T=null;var u=Q.p;try{Q.p=8,rc(t,e,n,l)}finally{Q.p=u,D.T=o}}function rc(t,e,n,l){if(hu){var o=cc(l);if(o===null)Wr(t,e,l,du,n),sd(t,l);else if(Pm(o,t,e,n,l))l.stopPropagation();else if(sd(t,l),e&4&&-1<qm.indexOf(t)){for(;o!==null;){var u=Gi(o);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=wi(u.pendingLanes);if(f!==0){var d=u;for(d.pendingLanes|=2,d.entangledLanes|=2;f;){var v=1<<31-Oe(f);d.entanglements[1]|=v,f&=~v}ji(u),(zt&6)===0&&(Wo=je()+500,zs(0))}}break;case 13:d=Gn(u,2),d!==null&&ii(d,u,2),Io(),uc(u,2)}if(u=cc(l),u===null&&Wr(t,e,l,du,n),u===o)break;o=u}o!==null&&l.stopPropagation()}else Wr(t,e,l,null,n)}}function cc(t){return t=Nn(t),fc(t)}var du=null;function fc(t){if(du=null,t=zi(t),t!==null){var e=Y(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=G(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return du=t,null}function ld(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Va()){case Ys:return 2;case Zl:return 8;case xn:case Vs:return 32;case Ul:return 268435456;default:return 32}default:return 32}}var hc=!1,ua=null,ra=null,ca=null,Bs=new Map,Zs=new Map,fa=[],qm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function sd(t,e){switch(t){case"focusin":case"focusout":ua=null;break;case"dragenter":case"dragleave":ra=null;break;case"mouseover":case"mouseout":ca=null;break;case"pointerover":case"pointerout":Bs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zs.delete(e.pointerId)}}function Us(t,e,n,l,o,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:u,targetContainers:[o]},e!==null&&(e=Gi(e),e!==null&&ad(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function Pm(t,e,n,l,o){switch(e){case"focusin":return ua=Us(ua,t,e,n,l,o),!0;case"dragenter":return ra=Us(ra,t,e,n,l,o),!0;case"mouseover":return ca=Us(ca,t,e,n,l,o),!0;case"pointerover":var u=o.pointerId;return Bs.set(u,Us(Bs.get(u)||null,t,e,n,l,o)),!0;case"gotpointercapture":return u=o.pointerId,Zs.set(u,Us(Zs.get(u)||null,t,e,n,l,o)),!0}return!1}function od(t){var e=zi(t.target);if(e!==null){var n=Y(e);if(n!==null){if(e=n.tag,e===13){if(e=G(n),e!==null){t.blockedOn=e,Pl(t.priority,function(){if(n.tag===13){var l=ei();l=Hl(l);var o=Gn(n,l);o!==null&&ii(o,n,l),uc(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function mu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=cc(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);ba=l,n.target.dispatchEvent(l),ba=null}else return e=Gi(n),e!==null&&ad(e),t.blockedOn=n,!1;e.shift()}return!0}function ud(t,e,n){mu(t)&&n.delete(e)}function km(){hc=!1,ua!==null&&mu(ua)&&(ua=null),ra!==null&&mu(ra)&&(ra=null),ca!==null&&mu(ca)&&(ca=null),Bs.forEach(ud),Zs.forEach(ud)}function _u(t,e){t.blockedOn===e&&(t.blockedOn=null,hc||(hc=!0,g.unstable_scheduleCallback(g.unstable_NormalPriority,km)))}var vu=null;function rd(t){vu!==t&&(vu=t,g.unstable_scheduleCallback(g.unstable_NormalPriority,function(){vu===t&&(vu=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],o=t[e+2];if(typeof l!="function"){if(fc(l||n)===null)continue;break}var u=Gi(n);u!==null&&(t.splice(e,3),e-=3,fr(u,{pending:!0,data:o,method:n.method,action:l},l,o))}}))}function js(t){function e(v){return _u(v,t)}ua!==null&&_u(ua,t),ra!==null&&_u(ra,t),ca!==null&&_u(ca,t),Bs.forEach(e),Zs.forEach(e);for(var n=0;n<fa.length;n++){var l=fa[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<fa.length&&(n=fa[0],n.blockedOn===null);)od(n),n.blockedOn===null&&fa.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var o=n[l],u=n[l+1],f=o[Te]||null;if(typeof u=="function")f||rd(n);else if(f){var d=null;if(u&&u.hasAttribute("formAction")){if(o=u,f=u[Te]||null)d=f.formAction;else if(fc(o)!==null)continue}else d=f.action;typeof d=="function"?n[l+1]=d:(n.splice(l,3),l-=3),rd(n)}}}function dc(t){this._internalRoot=t}pu.prototype.render=dc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(p(409));var n=e.current,l=ei();id(n,l,t,e,null,null)},pu.prototype.unmount=dc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;id(t.current,2,null,t,null,null),Io(),e[Tn]=null}};function pu(t){this._internalRoot=t}pu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Fs();t={blockedOn:null,target:t,priority:e};for(var n=0;n<fa.length&&e!==0&&e<fa[n].priority;n++);fa.splice(n,0,t),n===0&&od(t)}};var cd=E.version;if(cd!=="19.1.0")throw Error(p(527,cd,"19.1.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(p(188)):(t=Object.keys(t).join(","),Error(p(268,t)));return t=C(e),t=t!==null?B(t):null,t=t===null?null:t.stateNode,t};var Gm={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gu.isDisabled&&gu.supportsFiber)try{ki=gu.inject(Gm),Se=gu}catch{}}return qs.createRoot=function(t,e){if(!N(t))throw Error(p(299));var n=!1,l="",o=Ef,u=zf,f=Lf,d=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=td(t,1,!1,null,null,n,l,o,u,f,d,null),t[Tn]=e.current,Jr(t),new dc(e)},qs.hydrateRoot=function(t,e,n){if(!N(t))throw Error(p(299));var l=!1,o="",u=Ef,f=zf,d=Lf,v=null,w=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(d=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(v=n.unstable_transitionCallbacks),n.formState!==void 0&&(w=n.formState)),e=td(t,1,!0,e,n??null,l,o,u,f,d,v,w),e.context=ed(null),n=e.current,l=ei(),l=Hl(l),o=Kn(l),o.callback=null,Jn(n,o,l),n=l,e.current.lanes=n,Sn(e,n),ji(e),t[Tn]=e.current,Jr(t),new pu(e)},qs.version="19.1.0",qs}var bd;function $m(){if(bd)return vc.exports;bd=1;function g(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(g)}catch(E){console.error(E)}}return g(),vc.exports=Im(),vc.exports}var t_=$m();/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e_=g=>g.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i_=g=>g.replace(/^([A-Z])|[\s-_]+(\w)/g,(E,x,p)=>p?p.toUpperCase():x.toLowerCase()),Sd=g=>{const E=i_(g);return E.charAt(0).toUpperCase()+E.slice(1)},zd=(...g)=>g.filter((E,x,p)=>!!E&&E.trim()!==""&&p.indexOf(E)===x).join(" ").trim(),n_=g=>{for(const E in g)if(E.startsWith("aria-")||E==="role"||E==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a_={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l_=ie.forwardRef(({color:g="currentColor",size:E=24,strokeWidth:x=2,absoluteStrokeWidth:p,className:N="",children:Y,iconNode:G,...nt},C)=>ie.createElement("svg",{ref:C,...a_,width:E,height:E,stroke:g,strokeWidth:p?Number(x)*24/Number(E):x,className:zd("lucide",N),...!Y&&!n_(nt)&&{"aria-hidden":"true"},...nt},[...G.map(([B,q])=>ie.createElement(B,q)),...Array.isArray(Y)?Y:[Y]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=(g,E)=>{const x=ie.forwardRef(({className:p,...N},Y)=>ie.createElement(l_,{ref:Y,iconNode:E,className:zd(`lucide-${e_(Sd(g))}`,`lucide-${g}`,p),...N}));return x.displayName=Sd(g),x};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s_=[["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20",key:"oj1oa8"}],["path",{d:"m20 7 2 .5-2 .5",key:"12nv4d"}],["path",{d:"M10 18v3",key:"1yea0a"}],["path",{d:"M14 17.75V21",key:"1pymcb"}],["path",{d:"M7 18a6 6 0 0 0 3.84-10.61",key:"1npnn0"}]],o_=Le("bird",s_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u_=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],r_=Le("calendar",u_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c_=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],f_=Le("chart-column",c_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],d_=Le("circle-question-mark",h_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m_=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],__=Le("eye-off",m_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v_=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ld=Le("eye",v_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p_=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],g_=Le("funnel",p_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],x_=Le("globe",y_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],S_=Le("info",b_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T_=[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]],M_=Le("keyboard",T_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w_=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],E_=Le("loader-circle",w_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z_=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Ad=Le("map-pin",z_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L_=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],A_=Le("menu",L_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O_=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],xc=Le("search",O_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N_=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],C_=Le("settings",N_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D_=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Od=Le("x",D_),R_=()=>b.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:b.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:b.jsxs("div",{className:"flex justify-between items-center h-16",children:[b.jsx("div",{className:"flex items-center",children:b.jsxs("div",{className:"flex-shrink-0 flex items-center",children:[b.jsx(o_,{className:"h-8 w-8 text-blue-600"}),b.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900",children:"eBird"})]})}),b.jsxs("nav",{className:"hidden md:flex space-x-8",children:[b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"提交记录"}),b.jsx("a",{href:"#",className:"text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-blue-600",children:"探索"}),b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"我的eBird"}),b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"科学"}),b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"关于"}),b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"新闻"}),b.jsx("a",{href:"#",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"帮助"})]}),b.jsxs("div",{className:"flex items-center space-x-4",children:[b.jsxs("button",{className:"flex items-center text-gray-700 hover:text-blue-600",children:[b.jsx(x_,{className:"h-4 w-4 mr-1"}),b.jsx("span",{className:"text-sm",children:"中文"})]}),b.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"登录"}),b.jsx("button",{className:"md:hidden",children:b.jsx(A_,{className:"h-6 w-6 text-gray-700"})})]})]})})}),Td=g=>{let E;const x=new Set,p=(B,q)=>{const tt=typeof B=="function"?B(E):B;if(!Object.is(tt,E)){const ct=E;E=q??(typeof tt!="object"||tt===null)?tt:Object.assign({},E,tt),x.forEach(Pt=>Pt(E,ct))}},N=()=>E,nt={setState:p,getState:N,getInitialState:()=>C,subscribe:B=>(x.add(B),()=>x.delete(B))},C=E=g(p,N,nt);return nt},B_=g=>g?Td(g):Td,Z_=g=>g;function U_(g,E=Z_){const x=_d.useSyncExternalStore(g.subscribe,()=>E(g.getState()),()=>E(g.getInitialState()));return _d.useDebugValue(x),x}const Md=g=>{const E=B_(g),x=p=>U_(E,p);return Object.assign(x,E),x},j_=g=>g?Md(g):Md,bc=[{id:"sparrow",name:"麻雀",scientificName:"Passer domesticus",color:"#FF6B6B",description:"常见的城市鸟类",isVisible:!0},{id:"swallow",name:"燕子",scientificName:"Hirundo rustica",color:"#4ECDC4",description:"迁徙性鸟类",isVisible:!0},{id:"magpie",name:"喜鹊",scientificName:"Pica pica",color:"#45B7D1",description:"智慧的鸟类",isVisible:!0},{id:"pigeon",name:"鸽子",scientificName:"Columba livia",color:"#96CEB4",description:"城市适应性强的鸟类",isVisible:!0},{id:"robin",name:"知更鸟",scientificName:"Erithacus rubecula",color:"#FFEAA7",description:"小型鸣禽",isVisible:!0}],H_=()=>{const g=[],E=[{lat:39.9042,lng:116.4074,name:"北京"},{lat:31.2304,lng:121.4737,name:"上海"},{lat:23.1291,lng:113.2644,name:"广州"},{lat:30.5728,lng:104.0668,name:"成都"},{lat:29.563,lng:106.5516,name:"重庆"},{lat:38.0428,lng:114.5149,name:"石家庄"},{lat:36.0611,lng:103.8343,name:"兰州"},{lat:43.8256,lng:87.6168,name:"乌鲁木齐"}];return bc.forEach(x=>{E.forEach(p=>{for(let N=0;N<Math.random()*20+5;N++){const Y=(Math.random()-.5)*2,G=(Math.random()-.5)*2;g.push({lat:p.lat+Y,lng:p.lng+G,intensity:Math.random(),count:Math.floor(Math.random()*100)+1,speciesId:x.id})}})}),g},da=j_(g=>({species:[],selectedSpecies:[],searchQuery:"",locationFilter:"",dateFilter:"全年, 所有年份",mapCenter:[39.9042,116.4074],mapZoom:5,selectedMapType:"terrain",hotspots:[],observations:[],setSearchQuery:E=>g({searchQuery:E}),setLocationFilter:E=>g({locationFilter:E}),setDateFilter:E=>g({dateFilter:E}),toggleSpeciesVisibility:E=>g(x=>({species:x.species.map(p=>p.id===E?{...p,isVisible:!p.isVisible}:p)})),setSelectedSpecies:E=>g({selectedSpecies:E}),setMapCenter:E=>g({mapCenter:E}),setMapZoom:E=>g({mapZoom:E}),setSelectedMapType:E=>g({selectedMapType:E}),initializeData:()=>{const E=H_();g({species:bc,selectedSpecies:bc.map(x=>x.id),observations:E})}})),q_=()=>{const{searchQuery:g,locationFilter:E,dateFilter:x,setSearchQuery:p,setLocationFilter:N,setDateFilter:Y}=da();return b.jsx("div",{className:"bg-white border-b border-gray-200 px-4 py-4",children:b.jsx("div",{className:"max-w-7xl mx-auto",children:b.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[b.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6",children:[b.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"观鸟热点:"}),b.jsxs("div",{className:"relative",children:[b.jsx(xc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),b.jsx("input",{type:"text",placeholder:"输入观鸟热点名称…",value:g,onChange:G=>p(G.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full sm:w-80"})]})]}),b.jsxs("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(r_,{className:"h-4 w-4 text-gray-500"}),b.jsx("span",{className:"text-sm text-gray-700",children:"日期:"}),b.jsx("button",{onClick:()=>Y(x==="全年, 所有年份"?"本月":"全年, 所有年份"),className:"bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-md text-sm border border-gray-300",children:x})]}),b.jsxs("div",{className:"flex items-center space-x-2",children:[b.jsx(Ad,{className:"h-4 w-4 text-gray-500"}),b.jsx("span",{className:"text-sm text-gray-700",children:"地点:"}),b.jsxs("div",{className:"relative",children:[b.jsx(xc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400"}),b.jsx("input",{type:"text",placeholder:"搜索",value:E,onChange:G=>N(G.target.value),className:"pl-8 pr-4 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-32"})]})]})]})]})})})};var Ps={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var P_=Ps.exports,wd;function k_(){return wd||(wd=1,function(g,E){(function(x,p){p(E)})(P_,function(x){var p="1.9.4";function N(i){var a,s,r,c;for(s=1,r=arguments.length;s<r;s++){c=arguments[s];for(a in c)i[a]=c[a]}return i}var Y=Object.create||function(){function i(){}return function(a){return i.prototype=a,new i}}();function G(i,a){var s=Array.prototype.slice;if(i.bind)return i.bind.apply(i,s.call(arguments,1));var r=s.call(arguments,2);return function(){return i.apply(a,r.length?r.concat(s.call(arguments)):arguments)}}var nt=0;function C(i){return"_leaflet_id"in i||(i._leaflet_id=++nt),i._leaflet_id}function B(i,a,s){var r,c,h,_;return _=function(){r=!1,c&&(h.apply(s,c),c=!1)},h=function(){r?c=arguments:(i.apply(s,arguments),setTimeout(_,a),r=!0)},h}function q(i,a,s){var r=a[1],c=a[0],h=r-c;return i===r&&s?i:((i-c)%h+h)%h+c}function tt(){return!1}function ct(i,a){if(a===!1)return i;var s=Math.pow(10,a===void 0?6:a);return Math.round(i*s)/s}function Pt(i){return i.trim?i.trim():i.replace(/^\s+|\s+$/g,"")}function Ut(i){return Pt(i).split(/\s+/)}function mt(i,a){Object.prototype.hasOwnProperty.call(i,"options")||(i.options=i.options?Y(i.options):{});for(var s in a)i.options[s]=a[s];return i.options}function Xt(i,a,s){var r=[];for(var c in i)r.push(encodeURIComponent(s?c.toUpperCase():c)+"="+encodeURIComponent(i[c]));return(!a||a.indexOf("?")===-1?"?":"&")+r.join("&")}var ne=/\{ *([\w_ -]+) *\}/g;function Ae(i,a){return i.replace(ne,function(s,r){var c=a[r];if(c===void 0)throw new Error("No value provided for variable "+s);return typeof c=="function"&&(c=c(a)),c})}var Mt=Array.isArray||function(i){return Object.prototype.toString.call(i)==="[object Array]"};function fe(i,a){for(var s=0;s<i.length;s++)if(i[s]===a)return s;return-1}var ht="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function he(i){return window["webkit"+i]||window["moz"+i]||window["ms"+i]}var xe=0;function be(i){var a=+new Date,s=Math.max(0,16-(a-xe));return xe=a+s,window.setTimeout(i,s)}var Ue=window.requestAnimationFrame||he("RequestAnimationFrame")||be,Hi=window.cancelAnimationFrame||he("CancelAnimationFrame")||he("CancelRequestAnimationFrame")||function(i){window.clearTimeout(i)};function jt(i,a,s){if(s&&Ue===be)i.call(a);else return Ue.call(window,G(i,a))}function Et(i){i&&Hi.call(window,i)}var qi={__proto__:null,extend:N,create:Y,bind:G,get lastId(){return nt},stamp:C,throttle:B,wrapNum:q,falseFn:tt,formatNum:ct,trim:Pt,splitWords:Ut,setOptions:mt,getParamString:Xt,template:Ae,isArray:Mt,indexOf:fe,emptyImageUrl:ht,requestFn:Ue,cancelFn:Hi,requestAnimFrame:jt,cancelAnimFrame:Et};function de(){}de.extend=function(i){var a=function(){mt(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},s=a.__super__=this.prototype,r=Y(s);r.constructor=a,a.prototype=r;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(a[c]=this[c]);return i.statics&&N(a,i.statics),i.includes&&(ae(i.includes),N.apply(null,[r].concat(i.includes))),N(r,i),delete r.statics,delete r.includes,r.options&&(r.options=s.options?Y(s.options):{},N(r.options,i.options)),r._initHooks=[],r.callInitHooks=function(){if(!this._initHooksCalled){s.callInitHooks&&s.callInitHooks.call(this),this._initHooksCalled=!0;for(var h=0,_=r._initHooks.length;h<_;h++)r._initHooks[h].call(this)}},a},de.include=function(i){var a=this.prototype.options;return N(this.prototype,i),i.options&&(this.prototype.options=a,this.mergeOptions(i.options)),this},de.mergeOptions=function(i){return N(this.prototype.options,i),this},de.addInitHook=function(i){var a=Array.prototype.slice.call(arguments,1),s=typeof i=="function"?i:function(){this[i].apply(this,a)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(s),this};function ae(i){if(!(typeof L>"u"||!L||!L.Mixin)){i=Mt(i)?i:[i];for(var a=0;a<i.length;a++)i[a]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var D={on:function(i,a,s){if(typeof i=="object")for(var r in i)this._on(r,i[r],a);else{i=Ut(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s)}return this},off:function(i,a,s){if(!arguments.length)delete this._events;else if(typeof i=="object")for(var r in i)this._off(r,i[r],a);else{i=Ut(i);for(var c=arguments.length===1,h=0,_=i.length;h<_;h++)c?this._off(i[h]):this._off(i[h],a,s)}return this},_on:function(i,a,s,r){if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}if(this._listens(i,a,s)===!1){s===this&&(s=void 0);var c={fn:a,ctx:s};r&&(c.once=!0),this._events=this._events||{},this._events[i]=this._events[i]||[],this._events[i].push(c)}},_off:function(i,a,s){var r,c,h;if(this._events&&(r=this._events[i],!!r)){if(arguments.length===1){if(this._firingCount)for(c=0,h=r.length;c<h;c++)r[c].fn=tt;delete this._events[i];return}if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}var _=this._listens(i,a,s);if(_!==!1){var T=r[_];this._firingCount&&(T.fn=tt,this._events[i]=r=r.slice()),r.splice(_,1)}}},fire:function(i,a,s){if(!this.listens(i,s))return this;var r=N({},a,{type:i,target:this,sourceTarget:a&&a.sourceTarget||this});if(this._events){var c=this._events[i];if(c){this._firingCount=this._firingCount+1||1;for(var h=0,_=c.length;h<_;h++){var T=c[h],O=T.fn;T.once&&this.off(i,O,T.ctx),O.call(T.ctx||this,r)}this._firingCount--}}return s&&this._propagateEvent(r),this},listens:function(i,a,s,r){typeof i!="string"&&console.warn('"string" type argument expected');var c=a;typeof a!="function"&&(r=!!a,c=void 0,s=void 0);var h=this._events&&this._events[i];if(h&&h.length&&this._listens(i,c,s)!==!1)return!0;if(r){for(var _ in this._eventParents)if(this._eventParents[_].listens(i,a,s,r))return!0}return!1},_listens:function(i,a,s){if(!this._events)return!1;var r=this._events[i]||[];if(!a)return!!r.length;s===this&&(s=void 0);for(var c=0,h=r.length;c<h;c++)if(r[c].fn===a&&r[c].ctx===s)return c;return!1},once:function(i,a,s){if(typeof i=="object")for(var r in i)this._on(r,i[r],a,!0);else{i=Ut(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s,!0)}return this},addEventParent:function(i){return this._eventParents=this._eventParents||{},this._eventParents[C(i)]=i,this},removeEventParent:function(i){return this._eventParents&&delete this._eventParents[C(i)],this},_propagateEvent:function(i){for(var a in this._eventParents)this._eventParents[a].fire(i.type,N({layer:i.target,propagatedFrom:i.target},i),!0)}};D.addEventListener=D.on,D.removeEventListener=D.clearAllEventListeners=D.off,D.addOneTimeEventListener=D.once,D.fireEvent=D.fire,D.hasEventListeners=D.listens;var Q=de.extend(D);function P(i,a,s){this.x=s?Math.round(i):i,this.y=s?Math.round(a):a}var wt=Math.trunc||function(i){return i>0?Math.floor(i):Math.ceil(i)};P.prototype={clone:function(){return new P(this.x,this.y)},add:function(i){return this.clone()._add(m(i))},_add:function(i){return this.x+=i.x,this.y+=i.y,this},subtract:function(i){return this.clone()._subtract(m(i))},_subtract:function(i){return this.x-=i.x,this.y-=i.y,this},divideBy:function(i){return this.clone()._divideBy(i)},_divideBy:function(i){return this.x/=i,this.y/=i,this},multiplyBy:function(i){return this.clone()._multiplyBy(i)},_multiplyBy:function(i){return this.x*=i,this.y*=i,this},scaleBy:function(i){return new P(this.x*i.x,this.y*i.y)},unscaleBy:function(i){return new P(this.x/i.x,this.y/i.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=wt(this.x),this.y=wt(this.y),this},distanceTo:function(i){i=m(i);var a=i.x-this.x,s=i.y-this.y;return Math.sqrt(a*a+s*s)},equals:function(i){return i=m(i),i.x===this.x&&i.y===this.y},contains:function(i){return i=m(i),Math.abs(i.x)<=Math.abs(this.x)&&Math.abs(i.y)<=Math.abs(this.y)},toString:function(){return"Point("+ct(this.x)+", "+ct(this.y)+")"}};function m(i,a,s){return i instanceof P?i:Mt(i)?new P(i[0],i[1]):i==null?i:typeof i=="object"&&"x"in i&&"y"in i?new P(i.x,i.y):new P(i,a,s)}function R(i,a){if(i)for(var s=a?[i,a]:i,r=0,c=s.length;r<c;r++)this.extend(s[r])}R.prototype={extend:function(i){var a,s;if(!i)return this;if(i instanceof P||typeof i[0]=="number"||"x"in i)a=s=m(i);else if(i=V(i),a=i.min,s=i.max,!a||!s)return this;return!this.min&&!this.max?(this.min=a.clone(),this.max=s.clone()):(this.min.x=Math.min(a.x,this.min.x),this.max.x=Math.max(s.x,this.max.x),this.min.y=Math.min(a.y,this.min.y),this.max.y=Math.max(s.y,this.max.y)),this},getCenter:function(i){return m((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,i)},getBottomLeft:function(){return m(this.min.x,this.max.y)},getTopRight:function(){return m(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(i){var a,s;return typeof i[0]=="number"||i instanceof P?i=m(i):i=V(i),i instanceof R?(a=i.min,s=i.max):a=s=i,a.x>=this.min.x&&s.x<=this.max.x&&a.y>=this.min.y&&s.y<=this.max.y},intersects:function(i){i=V(i);var a=this.min,s=this.max,r=i.min,c=i.max,h=c.x>=a.x&&r.x<=s.x,_=c.y>=a.y&&r.y<=s.y;return h&&_},overlaps:function(i){i=V(i);var a=this.min,s=this.max,r=i.min,c=i.max,h=c.x>a.x&&r.x<s.x,_=c.y>a.y&&r.y<s.y;return h&&_},isValid:function(){return!!(this.min&&this.max)},pad:function(i){var a=this.min,s=this.max,r=Math.abs(a.x-s.x)*i,c=Math.abs(a.y-s.y)*i;return V(m(a.x-r,a.y-c),m(s.x+r,s.y+c))},equals:function(i){return i?(i=V(i),this.min.equals(i.getTopLeft())&&this.max.equals(i.getBottomRight())):!1}};function V(i,a){return!i||i instanceof R?i:new R(i,a)}function k(i,a){if(i)for(var s=a?[i,a]:i,r=0,c=s.length;r<c;r++)this.extend(s[r])}k.prototype={extend:function(i){var a=this._southWest,s=this._northEast,r,c;if(i instanceof I)r=i,c=i;else if(i instanceof k){if(r=i._southWest,c=i._northEast,!r||!c)return this}else return i?this.extend(X(i)||J(i)):this;return!a&&!s?(this._southWest=new I(r.lat,r.lng),this._northEast=new I(c.lat,c.lng)):(a.lat=Math.min(r.lat,a.lat),a.lng=Math.min(r.lng,a.lng),s.lat=Math.max(c.lat,s.lat),s.lng=Math.max(c.lng,s.lng)),this},pad:function(i){var a=this._southWest,s=this._northEast,r=Math.abs(a.lat-s.lat)*i,c=Math.abs(a.lng-s.lng)*i;return new k(new I(a.lat-r,a.lng-c),new I(s.lat+r,s.lng+c))},getCenter:function(){return new I((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new I(this.getNorth(),this.getWest())},getSouthEast:function(){return new I(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(i){typeof i[0]=="number"||i instanceof I||"lat"in i?i=X(i):i=J(i);var a=this._southWest,s=this._northEast,r,c;return i instanceof k?(r=i.getSouthWest(),c=i.getNorthEast()):r=c=i,r.lat>=a.lat&&c.lat<=s.lat&&r.lng>=a.lng&&c.lng<=s.lng},intersects:function(i){i=J(i);var a=this._southWest,s=this._northEast,r=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>=a.lat&&r.lat<=s.lat,_=c.lng>=a.lng&&r.lng<=s.lng;return h&&_},overlaps:function(i){i=J(i);var a=this._southWest,s=this._northEast,r=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>a.lat&&r.lat<s.lat,_=c.lng>a.lng&&r.lng<s.lng;return h&&_},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(i,a){return i?(i=J(i),this._southWest.equals(i.getSouthWest(),a)&&this._northEast.equals(i.getNorthEast(),a)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function J(i,a){return i instanceof k?i:new k(i,a)}function I(i,a,s){if(isNaN(i)||isNaN(a))throw new Error("Invalid LatLng object: ("+i+", "+a+")");this.lat=+i,this.lng=+a,s!==void 0&&(this.alt=+s)}I.prototype={equals:function(i,a){if(!i)return!1;i=X(i);var s=Math.max(Math.abs(this.lat-i.lat),Math.abs(this.lng-i.lng));return s<=(a===void 0?1e-9:a)},toString:function(i){return"LatLng("+ct(this.lat,i)+", "+ct(this.lng,i)+")"},distanceTo:function(i){return bt.distance(this,X(i))},wrap:function(){return bt.wrapLatLng(this)},toBounds:function(i){var a=180*i/40075017,s=a/Math.cos(Math.PI/180*this.lat);return J([this.lat-a,this.lng-s],[this.lat+a,this.lng+s])},clone:function(){return new I(this.lat,this.lng,this.alt)}};function X(i,a,s){return i instanceof I?i:Mt(i)&&typeof i[0]!="object"?i.length===3?new I(i[0],i[1],i[2]):i.length===2?new I(i[0],i[1]):null:i==null?i:typeof i=="object"&&"lat"in i?new I(i.lat,"lng"in i?i.lng:i.lon,i.alt):a===void 0?null:new I(i,a,s)}var kt={latLngToPoint:function(i,a){var s=this.projection.project(i),r=this.scale(a);return this.transformation._transform(s,r)},pointToLatLng:function(i,a){var s=this.scale(a),r=this.transformation.untransform(i,s);return this.projection.unproject(r)},project:function(i){return this.projection.project(i)},unproject:function(i){return this.projection.unproject(i)},scale:function(i){return 256*Math.pow(2,i)},zoom:function(i){return Math.log(i/256)/Math.LN2},getProjectedBounds:function(i){if(this.infinite)return null;var a=this.projection.bounds,s=this.scale(i),r=this.transformation.transform(a.min,s),c=this.transformation.transform(a.max,s);return new R(r,c)},infinite:!1,wrapLatLng:function(i){var a=this.wrapLng?q(i.lng,this.wrapLng,!0):i.lng,s=this.wrapLat?q(i.lat,this.wrapLat,!0):i.lat,r=i.alt;return new I(s,a,r)},wrapLatLngBounds:function(i){var a=i.getCenter(),s=this.wrapLatLng(a),r=a.lat-s.lat,c=a.lng-s.lng;if(r===0&&c===0)return i;var h=i.getSouthWest(),_=i.getNorthEast(),T=new I(h.lat-r,h.lng-c),O=new I(_.lat-r,_.lng-c);return new k(T,O)}},bt=N({},kt,{wrapLng:[-180,180],R:6371e3,distance:function(i,a){var s=Math.PI/180,r=i.lat*s,c=a.lat*s,h=Math.sin((a.lat-i.lat)*s/2),_=Math.sin((a.lng-i.lng)*s/2),T=h*h+Math.cos(r)*Math.cos(c)*_*_,O=2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T));return this.R*O}}),mi=6378137,ma={R:mi,MAX_LATITUDE:85.0511287798,project:function(i){var a=Math.PI/180,s=this.MAX_LATITUDE,r=Math.max(Math.min(s,i.lat),-s),c=Math.sin(r*a);return new P(this.R*i.lng*a,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(i){var a=180/Math.PI;return new I((2*Math.atan(Math.exp(i.y/this.R))-Math.PI/2)*a,i.x*a/this.R)},bounds:function(){var i=mi*Math.PI;return new R([-i,-i],[i,i])}()};function yn(i,a,s,r){if(Mt(i)){this._a=i[0],this._b=i[1],this._c=i[2],this._d=i[3];return}this._a=i,this._b=a,this._c=s,this._d=r}yn.prototype={transform:function(i,a){return this._transform(i.clone(),a)},_transform:function(i,a){return a=a||1,i.x=a*(this._a*i.x+this._b),i.y=a*(this._c*i.y+this._d),i},untransform:function(i,a){return a=a||1,new P((i.x/a-this._b)/this._a,(i.y/a-this._d)/this._c)}};function Pi(i,a,s,r){return new yn(i,a,s,r)}var _a=N({},bt,{code:"EPSG:3857",projection:ma,transformation:function(){var i=.5/(Math.PI*ma.R);return Pi(i,.5,-i,.5)}()}),Bl=N({},_a,{code:"EPSG:900913"});function ks(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function Gs(i,a){var s="",r,c,h,_,T,O;for(r=0,h=i.length;r<h;r++){for(T=i[r],c=0,_=T.length;c<_;c++)O=T[c],s+=(c?"L":"M")+O.x+" "+O.y;s+=a?et.svg?"z":"x":""}return s||"M0 0"}var je=document.documentElement.style,Va="ActiveXObject"in window,Ys=Va&&!document.addEventListener,Zl="msLaunchUri"in navigator&&!("documentMode"in document),xn=He("webkit"),Vs=He("android"),Ul=He("android 2")||He("android 3"),yu=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),xu=Vs&&He("Google")&&yu<537&&!("AudioNode"in window),ki=!!window.opera,Se=!Zl&&He("chrome"),_i=He("gecko")&&!xn&&!ki&&!Va,Oe=!Se&&He("safari"),Xs=He("phantom"),Qs="OTransition"in je,bu=navigator.platform.indexOf("Win")===0,va=Va&&"transition"in je,bn="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!Ul,wi="MozPerspective"in je,Xa=!window.L_DISABLE_3D&&(va||bn||wi)&&!Qs&&!Xs,vi=typeof orientation<"u"||He("mobile"),Su=vi&&xn,Ks=vi&&bn,jl=!window.PointerEvent&&window.MSPointerEvent,Qa=!!(window.PointerEvent||jl),Sn="ontouchstart"in window||!!window.TouchEvent,Tu=!window.L_NO_TOUCH&&(Sn||Qa),Js=vi&&ki,Ws=vi&&_i,Hl=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,ql=function(){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){i=!0}});window.addEventListener("testPassiveEventSupport",tt,a),window.removeEventListener("testPassiveEventSupport",tt,a)}catch{}return i}(),Fs=function(){return!!document.createElement("canvas").getContext}(),Pl=!!(document.createElementNS&&ks("svg").createSVGRect),Ei=!!Pl&&function(){var i=document.createElement("div");return i.innerHTML="<svg/>",(i.firstChild&&i.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),le=!Pl&&function(){try{var i=document.createElement("div");i.innerHTML='<v:shape adj="1"/>';var a=i.firstChild;return a.style.behavior="url(#default#VML)",a&&typeof a.adj=="object"}catch{return!1}}(),Te=navigator.platform.indexOf("Mac")===0,Tn=navigator.platform.indexOf("Linux")===0;function He(i){return navigator.userAgent.toLowerCase().indexOf(i)>=0}var et={ie:Va,ielt9:Ys,edge:Zl,webkit:xn,android:Vs,android23:Ul,androidStock:xu,opera:ki,chrome:Se,gecko:_i,safari:Oe,phantom:Xs,opera12:Qs,win:bu,ie3d:va,webkit3d:bn,gecko3d:wi,any3d:Xa,mobile:vi,mobileWebkit:Su,mobileWebkit3d:Ks,msPointer:jl,pointer:Qa,touch:Tu,touchNative:Sn,mobileOpera:Js,mobileGecko:Ws,retina:Hl,passiveEvents:ql,canvas:Fs,svg:Pl,vml:le,inlineSvg:Ei,mac:Te,linux:Tn},Is=et.msPointer?"MSPointerDown":"pointerdown",kl=et.msPointer?"MSPointerMove":"pointermove",Mn=et.msPointer?"MSPointerUp":"pointerup",Ka=et.msPointer?"MSPointerCancel":"pointercancel",zi={touchstart:Is,touchmove:kl,touchend:Mn,touchcancel:Ka},Gi={touchstart:eo,touchmove:pa,touchend:pa,touchcancel:pa},ni={},Yi=!1;function Ft(i,a,s){return a==="touchstart"&&Mu(),Gi[a]?(s=Gi[a].bind(this,s),i.addEventListener(zi[a],s,!1),s):(console.warn("wrong event specified:",a),tt)}function $s(i,a,s){if(!zi[a]){console.warn("wrong event specified:",a);return}i.removeEventListener(zi[a],s,!1)}function to(i){ni[i.pointerId]=i}function Vi(i){ni[i.pointerId]&&(ni[i.pointerId]=i)}function Xi(i){delete ni[i.pointerId]}function Mu(){Yi||(document.addEventListener(Is,to,!0),document.addEventListener(kl,Vi,!0),document.addEventListener(Mn,Xi,!0),document.addEventListener(Ka,Xi,!0),Yi=!0)}function pa(i,a){if(a.pointerType!==(a.MSPOINTER_TYPE_MOUSE||"mouse")){a.touches=[];for(var s in ni)a.touches.push(ni[s]);a.changedTouches=[a],i(a)}}function eo(i,a){a.MSPOINTER_TYPE_TOUCH&&a.pointerType===a.MSPOINTER_TYPE_TOUCH&&Qt(a),pa(i,a)}function wu(i){var a={},s,r;for(r in i)s=i[r],a[r]=s&&s.bind?s.bind(i):s;return i=a,a.type="dblclick",a.detail=2,a.isTrusted=!1,a._simulated=!0,a}var Ja=200;function Wa(i,a){i.addEventListener("dblclick",a);var s=0,r;function c(h){if(h.detail!==1){r=h.detail;return}if(!(h.pointerType==="mouse"||h.sourceCapabilities&&!h.sourceCapabilities.firesTouchEvents)){var _=Vl(h);if(!(_.some(function(O){return O instanceof HTMLLabelElement&&O.attributes.for})&&!_.some(function(O){return O instanceof HTMLInputElement||O instanceof HTMLSelectElement}))){var T=Date.now();T-s<=Ja?(r++,r===2&&a(wu(h))):r=1,s=T}}}return i.addEventListener("click",c),{dblclick:a,simDblclick:c}}function pi(i,a){i.removeEventListener("dblclick",a.dblclick),i.removeEventListener("click",a.simDblclick)}var ga=Ln(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),wn=Ln(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Qi=wn==="webkitTransition"||wn==="OTransition"?wn+"End":"transitionend";function Fa(i){return typeof i=="string"?document.getElementById(i):i}function Ki(i,a){var s=i.style[a]||i.currentStyle&&i.currentStyle[a];if((!s||s==="auto")&&document.defaultView){var r=document.defaultView.getComputedStyle(i,null);s=r?r[a]:null}return s==="auto"?null:s}function yt(i,a,s){var r=document.createElement(i);return r.className=a||"",s&&s.appendChild(r),r}function Dt(i){var a=i.parentNode;a&&a.removeChild(i)}function me(i){for(;i.firstChild;)i.removeChild(i.firstChild)}function Ji(i){var a=i.parentNode;a&&a.lastChild!==i&&a.appendChild(i)}function En(i){var a=i.parentNode;a&&a.firstChild!==i&&a.insertBefore(i,a.firstChild)}function zn(i,a){if(i.classList!==void 0)return i.classList.contains(a);var s=_e(i);return s.length>0&&new RegExp("(^|\\s)"+a+"(\\s|$)").test(s)}function rt(i,a){if(i.classList!==void 0)for(var s=Ut(a),r=0,c=s.length;r<c;r++)i.classList.add(s[r]);else if(!zn(i,a)){var h=_e(i);Gl(i,(h?h+" ":"")+a)}}function Rt(i,a){i.classList!==void 0?i.classList.remove(a):Gl(i,Pt((" "+_e(i)+" ").replace(" "+a+" "," ")))}function Gl(i,a){i.className.baseVal===void 0?i.className=a:i.className.baseVal=a}function _e(i){return i.correspondingElement&&(i=i.correspondingElement),i.className.baseVal===void 0?i.className:i.className.baseVal}function Me(i,a){"opacity"in i.style?i.style.opacity=a:"filter"in i.style&&io(i,a)}function io(i,a){var s=!1,r="DXImageTransform.Microsoft.Alpha";try{s=i.filters.item(r)}catch{if(a===1)return}a=Math.round(a*100),s?(s.Enabled=a!==100,s.Opacity=a):i.style.filter+=" progid:"+r+"(opacity="+a+")"}function Ln(i){for(var a=document.documentElement.style,s=0;s<i.length;s++)if(i[s]in a)return i[s];return!1}function qe(i,a,s){var r=a||new P(0,0);i.style[ga]=(et.ie3d?"translate("+r.x+"px,"+r.y+"px)":"translate3d("+r.x+"px,"+r.y+"px,0)")+(s?" scale("+s+")":"")}function Gt(i,a){i._leaflet_pos=a,et.any3d?qe(i,a):(i.style.left=a.x+"px",i.style.top=a.y+"px")}function Li(i){return i._leaflet_pos||new P(0,0)}var ai,ya,Ia;if("onselectstart"in document)ai=function(){ut(window,"selectstart",Qt)},ya=function(){St(window,"selectstart",Qt)};else{var An=Ln(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);ai=function(){if(An){var i=document.documentElement.style;Ia=i[An],i[An]="none"}},ya=function(){An&&(document.documentElement.style[An]=Ia,Ia=void 0)}}function xa(){ut(window,"dragstart",Qt)}function Yl(){St(window,"dragstart",Qt)}var $a,On;function ba(i){for(;i.tabIndex===-1;)i=i.parentNode;i.style&&(Nn(),$a=i,On=i.style.outlineStyle,i.style.outlineStyle="none",ut(window,"keydown",Nn))}function Nn(){$a&&($a.style.outlineStyle=On,$a=void 0,On=void 0,St(window,"keydown",Nn))}function Wi(i){do i=i.parentNode;while((!i.offsetWidth||!i.offsetHeight)&&i!==document.body);return i}function Ai(i){var a=i.getBoundingClientRect();return{x:a.width/i.offsetWidth||1,y:a.height/i.offsetHeight||1,boundingClientRect:a}}var no={__proto__:null,TRANSFORM:ga,TRANSITION:wn,TRANSITION_END:Qi,get:Fa,getStyle:Ki,create:yt,remove:Dt,empty:me,toFront:Ji,toBack:En,hasClass:zn,addClass:rt,removeClass:Rt,setClass:Gl,getClass:_e,setOpacity:Me,testProp:Ln,setTransform:qe,setPosition:Gt,getPosition:Li,get disableTextSelection(){return ai},get enableTextSelection(){return ya},disableImageDrag:xa,enableImageDrag:Yl,preventOutline:ba,restoreOutline:Nn,getSizedParentNode:Wi,getScale:Ai};function ut(i,a,s,r){if(a&&typeof a=="object")for(var c in a)Fi(i,c,a[c],s);else{a=Ut(a);for(var h=0,_=a.length;h<_;h++)Fi(i,a[h],s,r)}return this}var Ve="_leaflet_events";function St(i,a,s,r){if(arguments.length===1)li(i),delete i[Ve];else if(a&&typeof a=="object")for(var c in a)si(i,c,a[c],s);else if(a=Ut(a),arguments.length===2)li(i,function(T){return fe(a,T)!==-1});else for(var h=0,_=a.length;h<_;h++)si(i,a[h],s,r);return this}function li(i,a){for(var s in i[Ve]){var r=s.split(/\d/)[0];(!a||a(r))&&si(i,r,null,null,s)}}var Sa={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Fi(i,a,s,r){var c=a+C(s)+(r?"_"+C(r):"");if(i[Ve]&&i[Ve][c])return this;var h=function(T){return s.call(r||i,T||window.event)},_=h;!et.touchNative&&et.pointer&&a.indexOf("touch")===0?h=Ft(i,a,h):et.touch&&a==="dblclick"?h=Wa(i,h):"addEventListener"in i?a==="touchstart"||a==="touchmove"||a==="wheel"||a==="mousewheel"?i.addEventListener(Sa[a]||a,h,et.passiveEvents?{passive:!1}:!1):a==="mouseenter"||a==="mouseleave"?(h=function(T){T=T||window.event,$i(i,T)&&_(T)},i.addEventListener(Sa[a],h,!1)):i.addEventListener(a,_,!1):i.attachEvent("on"+a,h),i[Ve]=i[Ve]||{},i[Ve][c]=h}function si(i,a,s,r,c){c=c||a+C(s)+(r?"_"+C(r):"");var h=i[Ve]&&i[Ve][c];if(!h)return this;!et.touchNative&&et.pointer&&a.indexOf("touch")===0?$s(i,a,h):et.touch&&a==="dblclick"?pi(i,h):"removeEventListener"in i?i.removeEventListener(Sa[a]||a,h,!1):i.detachEvent("on"+a,h),i[Ve][c]=null}function gi(i){return i.stopPropagation?i.stopPropagation():i.originalEvent?i.originalEvent._stopped=!0:i.cancelBubble=!0,this}function Cn(i){return Fi(i,"wheel",gi),this}function Dn(i){return ut(i,"mousedown touchstart dblclick contextmenu",gi),i._leaflet_disable_click=!0,this}function Qt(i){return i.preventDefault?i.preventDefault():i.returnValue=!1,this}function oi(i){return Qt(i),gi(i),this}function Vl(i){if(i.composedPath)return i.composedPath();for(var a=[],s=i.target;s;)a.push(s),s=s.parentNode;return a}function ve(i,a){if(!a)return new P(i.clientX,i.clientY);var s=Ai(a),r=s.boundingClientRect;return new P((i.clientX-r.left)/s.x-a.clientLeft,(i.clientY-r.top)/s.y-a.clientTop)}var Ii=et.linux&&et.chrome?window.devicePixelRatio:et.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Ta(i){return et.edge?i.wheelDeltaY/2:i.deltaY&&i.deltaMode===0?-i.deltaY/Ii:i.deltaY&&i.deltaMode===1?-i.deltaY*20:i.deltaY&&i.deltaMode===2?-i.deltaY*60:i.deltaX||i.deltaZ?0:i.wheelDelta?(i.wheelDeltaY||i.wheelDelta)/2:i.detail&&Math.abs(i.detail)<32765?-i.detail*20:i.detail?i.detail/-32765*60:0}function $i(i,a){var s=a.relatedTarget;if(!s)return!0;try{for(;s&&s!==i;)s=s.parentNode}catch{return!1}return s!==i}var Eu={__proto__:null,on:ut,off:St,stopPropagation:gi,disableScrollPropagation:Cn,disableClickPropagation:Dn,preventDefault:Qt,stop:oi,getPropagationPath:Vl,getMousePosition:ve,getWheelDelta:Ta,isExternalTarget:$i,addListener:ut,removeListener:St},tl=Q.extend({run:function(i,a,s,r){this.stop(),this._el=i,this._inProgress=!0,this._duration=s||.25,this._easeOutPower=1/Math.max(r||.5,.2),this._startPos=Li(i),this._offset=a.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=jt(this._animate,this),this._step()},_step:function(i){var a=+new Date-this._startTime,s=this._duration*1e3;a<s?this._runFrame(this._easeOut(a/s),i):(this._runFrame(1),this._complete())},_runFrame:function(i,a){var s=this._startPos.add(this._offset.multiplyBy(i));a&&s._round(),Gt(this._el,s),this.fire("step")},_complete:function(){Et(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(i){return 1-Math.pow(1-i,this._easeOutPower)}}),_t=Q.extend({options:{crs:_a,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(i,a){a=mt(this,a),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(i),this._initLayout(),this._onResize=G(this._onResize,this),this._initEvents(),a.maxBounds&&this.setMaxBounds(a.maxBounds),a.zoom!==void 0&&(this._zoom=this._limitZoom(a.zoom)),a.center&&a.zoom!==void 0&&this.setView(X(a.center),a.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=wn&&et.any3d&&!et.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),ut(this._proxy,Qi,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(i,a,s){if(a=a===void 0?this._zoom:this._limitZoom(a),i=this._limitCenter(X(i),a,this.options.maxBounds),s=s||{},this._stop(),this._loaded&&!s.reset&&s!==!0){s.animate!==void 0&&(s.zoom=N({animate:s.animate},s.zoom),s.pan=N({animate:s.animate,duration:s.duration},s.pan));var r=this._zoom!==a?this._tryAnimatedZoom&&this._tryAnimatedZoom(i,a,s.zoom):this._tryAnimatedPan(i,s.pan);if(r)return clearTimeout(this._sizeTimer),this}return this._resetView(i,a,s.pan&&s.pan.noMoveStart),this},setZoom:function(i,a){return this._loaded?this.setView(this.getCenter(),i,{zoom:a}):(this._zoom=i,this)},zoomIn:function(i,a){return i=i||(et.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+i,a)},zoomOut:function(i,a){return i=i||(et.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-i,a)},setZoomAround:function(i,a,s){var r=this.getZoomScale(a),c=this.getSize().divideBy(2),h=i instanceof P?i:this.latLngToContainerPoint(i),_=h.subtract(c).multiplyBy(1-1/r),T=this.containerPointToLatLng(c.add(_));return this.setView(T,a,{zoom:s})},_getBoundsCenterZoom:function(i,a){a=a||{},i=i.getBounds?i.getBounds():J(i);var s=m(a.paddingTopLeft||a.padding||[0,0]),r=m(a.paddingBottomRight||a.padding||[0,0]),c=this.getBoundsZoom(i,!1,s.add(r));if(c=typeof a.maxZoom=="number"?Math.min(a.maxZoom,c):c,c===1/0)return{center:i.getCenter(),zoom:c};var h=r.subtract(s).divideBy(2),_=this.project(i.getSouthWest(),c),T=this.project(i.getNorthEast(),c),O=this.unproject(_.add(T).divideBy(2).add(h),c);return{center:O,zoom:c}},fitBounds:function(i,a){if(i=J(i),!i.isValid())throw new Error("Bounds are not valid.");var s=this._getBoundsCenterZoom(i,a);return this.setView(s.center,s.zoom,a)},fitWorld:function(i){return this.fitBounds([[-90,-180],[90,180]],i)},panTo:function(i,a){return this.setView(i,this._zoom,{pan:a})},panBy:function(i,a){if(i=m(i).round(),a=a||{},!i.x&&!i.y)return this.fire("moveend");if(a.animate!==!0&&!this.getSize().contains(i))return this._resetView(this.unproject(this.project(this.getCenter()).add(i)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new tl,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),a.noMoveStart||this.fire("movestart"),a.animate!==!1){rt(this._mapPane,"leaflet-pan-anim");var s=this._getMapPanePos().subtract(i).round();this._panAnim.run(this._mapPane,s,a.duration||.25,a.easeLinearity)}else this._rawPanBy(i),this.fire("move").fire("moveend");return this},flyTo:function(i,a,s){if(s=s||{},s.animate===!1||!et.any3d)return this.setView(i,a,s);this._stop();var r=this.project(this.getCenter()),c=this.project(i),h=this.getSize(),_=this._zoom;i=X(i),a=a===void 0?_:a;var T=Math.max(h.x,h.y),O=T*this.getZoomScale(_,a),H=c.distanceTo(r)||1,K=1.42,W=K*K;function $(Ht){var ci=Ht?-1:1,bi=Ht?O:T,Ri=O*O-T*T+ci*W*W*H*H,Si=2*bi*W*H,Za=Ri/Si,dl=Math.sqrt(Za*Za+1)-Za,Ua=dl<1e-9?-18:Math.log(dl);return Ua}function ot(Ht){return(Math.exp(Ht)-Math.exp(-Ht))/2}function Bt(Ht){return(Math.exp(Ht)+Math.exp(-Ht))/2}function Yt(Ht){return ot(Ht)/Bt(Ht)}var oe=$(0);function Pe(Ht){return T*(Bt(oe)/Bt(oe+K*Ht))}function To(Ht){return T*(Bt(oe)*Yt(oe+K*Ht)-ot(oe))/W}function Mo(Ht){return 1-Math.pow(1-Ht,1.5)}var Ba=Date.now(),Vn=($(1)-oe)/K,wo=s.duration?1e3*s.duration:1e3*Vn*.8;function Xn(){var Ht=(Date.now()-Ba)/wo,ci=Mo(Ht)*Vn;Ht<=1?(this._flyToFrame=jt(Xn,this),this._move(this.unproject(r.add(c.subtract(r).multiplyBy(To(ci)/H)),_),this.getScaleZoom(T/Pe(ci),_),{flyTo:!0})):this._move(i,a)._moveEnd(!0)}return this._moveStart(!0,s.noMoveStart),Xn.call(this),this},flyToBounds:function(i,a){var s=this._getBoundsCenterZoom(i,a);return this.flyTo(s.center,s.zoom,a)},setMaxBounds:function(i){return i=J(i),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),i.isValid()?(this.options.maxBounds=i,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(i){var a=this.options.minZoom;return this.options.minZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(i):this},setMaxZoom:function(i){var a=this.options.maxZoom;return this.options.maxZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(i):this},panInsideBounds:function(i,a){this._enforcingBounds=!0;var s=this.getCenter(),r=this._limitCenter(s,this._zoom,J(i));return s.equals(r)||this.panTo(r,a),this._enforcingBounds=!1,this},panInside:function(i,a){a=a||{};var s=m(a.paddingTopLeft||a.padding||[0,0]),r=m(a.paddingBottomRight||a.padding||[0,0]),c=this.project(this.getCenter()),h=this.project(i),_=this.getPixelBounds(),T=V([_.min.add(s),_.max.subtract(r)]),O=T.getSize();if(!T.contains(h)){this._enforcingBounds=!0;var H=h.subtract(T.getCenter()),K=T.extend(h).getSize().subtract(O);c.x+=H.x<0?-K.x:K.x,c.y+=H.y<0?-K.y:K.y,this.panTo(this.unproject(c),a),this._enforcingBounds=!1}return this},invalidateSize:function(i){if(!this._loaded)return this;i=N({animate:!1,pan:!0},i===!0?{animate:!0}:i);var a=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var s=this.getSize(),r=a.divideBy(2).round(),c=s.divideBy(2).round(),h=r.subtract(c);return!h.x&&!h.y?this:(i.animate&&i.pan?this.panBy(h):(i.pan&&this._rawPanBy(h),this.fire("move"),i.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(G(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:a,newSize:s}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(i){if(i=this._locateOptions=N({timeout:1e4,watch:!1},i),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var a=G(this._handleGeolocationResponse,this),s=G(this._handleGeolocationError,this);return i.watch?this._locationWatchId=navigator.geolocation.watchPosition(a,s,i):navigator.geolocation.getCurrentPosition(a,s,i),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(i){if(this._container._leaflet_id){var a=i.code,s=i.message||(a===1?"permission denied":a===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:a,message:"Geolocation error: "+s+"."})}},_handleGeolocationResponse:function(i){if(this._container._leaflet_id){var a=i.coords.latitude,s=i.coords.longitude,r=new I(a,s),c=r.toBounds(i.coords.accuracy*2),h=this._locateOptions;if(h.setView){var _=this.getBoundsZoom(c);this.setView(r,h.maxZoom?Math.min(_,h.maxZoom):_)}var T={latlng:r,bounds:c,timestamp:i.timestamp};for(var O in i.coords)typeof i.coords[O]=="number"&&(T[O]=i.coords[O]);this.fire("locationfound",T)}},addHandler:function(i,a){if(!a)return this;var s=this[i]=new a(this);return this._handlers.push(s),this.options[i]&&s.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Dt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Et(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var i;for(i in this._layers)this._layers[i].remove();for(i in this._panes)Dt(this._panes[i]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(i,a){var s="leaflet-pane"+(i?" leaflet-"+i.replace("Pane","")+"-pane":""),r=yt("div",s,a||this._mapPane);return i&&(this._panes[i]=r),r},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var i=this.getPixelBounds(),a=this.unproject(i.getBottomLeft()),s=this.unproject(i.getTopRight());return new k(a,s)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(i,a,s){i=J(i),s=m(s||[0,0]);var r=this.getZoom()||0,c=this.getMinZoom(),h=this.getMaxZoom(),_=i.getNorthWest(),T=i.getSouthEast(),O=this.getSize().subtract(s),H=V(this.project(T,r),this.project(_,r)).getSize(),K=et.any3d?this.options.zoomSnap:1,W=O.x/H.x,$=O.y/H.y,ot=a?Math.max(W,$):Math.min(W,$);return r=this.getScaleZoom(ot,r),K&&(r=Math.round(r/(K/100))*(K/100),r=a?Math.ceil(r/K)*K:Math.floor(r/K)*K),Math.max(c,Math.min(h,r))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new P(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(i,a){var s=this._getTopLeftPoint(i,a);return new R(s,s.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(i){return this.options.crs.getProjectedBounds(i===void 0?this.getZoom():i)},getPane:function(i){return typeof i=="string"?this._panes[i]:i},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(i,a){var s=this.options.crs;return a=a===void 0?this._zoom:a,s.scale(i)/s.scale(a)},getScaleZoom:function(i,a){var s=this.options.crs;a=a===void 0?this._zoom:a;var r=s.zoom(i*s.scale(a));return isNaN(r)?1/0:r},project:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.latLngToPoint(X(i),a)},unproject:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.pointToLatLng(m(i),a)},layerPointToLatLng:function(i){var a=m(i).add(this.getPixelOrigin());return this.unproject(a)},latLngToLayerPoint:function(i){var a=this.project(X(i))._round();return a._subtract(this.getPixelOrigin())},wrapLatLng:function(i){return this.options.crs.wrapLatLng(X(i))},wrapLatLngBounds:function(i){return this.options.crs.wrapLatLngBounds(J(i))},distance:function(i,a){return this.options.crs.distance(X(i),X(a))},containerPointToLayerPoint:function(i){return m(i).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(i){return m(i).add(this._getMapPanePos())},containerPointToLatLng:function(i){var a=this.containerPointToLayerPoint(m(i));return this.layerPointToLatLng(a)},latLngToContainerPoint:function(i){return this.layerPointToContainerPoint(this.latLngToLayerPoint(X(i)))},mouseEventToContainerPoint:function(i){return ve(i,this._container)},mouseEventToLayerPoint:function(i){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(i))},mouseEventToLatLng:function(i){return this.layerPointToLatLng(this.mouseEventToLayerPoint(i))},_initContainer:function(i){var a=this._container=Fa(i);if(a){if(a._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");ut(a,"scroll",this._onScroll,this),this._containerId=C(a)},_initLayout:function(){var i=this._container;this._fadeAnimated=this.options.fadeAnimation&&et.any3d,rt(i,"leaflet-container"+(et.touch?" leaflet-touch":"")+(et.retina?" leaflet-retina":"")+(et.ielt9?" leaflet-oldie":"")+(et.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var a=Ki(i,"position");a!=="absolute"&&a!=="relative"&&a!=="fixed"&&a!=="sticky"&&(i.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var i=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Gt(this._mapPane,new P(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(rt(i.markerPane,"leaflet-zoom-hide"),rt(i.shadowPane,"leaflet-zoom-hide"))},_resetView:function(i,a,s){Gt(this._mapPane,new P(0,0));var r=!this._loaded;this._loaded=!0,a=this._limitZoom(a),this.fire("viewprereset");var c=this._zoom!==a;this._moveStart(c,s)._move(i,a)._moveEnd(c),this.fire("viewreset"),r&&this.fire("load")},_moveStart:function(i,a){return i&&this.fire("zoomstart"),a||this.fire("movestart"),this},_move:function(i,a,s,r){a===void 0&&(a=this._zoom);var c=this._zoom!==a;return this._zoom=a,this._lastCenter=i,this._pixelOrigin=this._getNewPixelOrigin(i),r?s&&s.pinch&&this.fire("zoom",s):((c||s&&s.pinch)&&this.fire("zoom",s),this.fire("move",s)),this},_moveEnd:function(i){return i&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Et(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(i){Gt(this._mapPane,this._getMapPanePos().subtract(i))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(i){this._targets={},this._targets[C(this._container)]=this;var a=i?St:ut;a(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&a(window,"resize",this._onResize,this),et.any3d&&this.options.transform3DLimit&&(i?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Et(this._resizeRequest),this._resizeRequest=jt(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var i=this._getMapPanePos();Math.max(Math.abs(i.x),Math.abs(i.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(i,a){for(var s=[],r,c=a==="mouseout"||a==="mouseover",h=i.target||i.srcElement,_=!1;h;){if(r=this._targets[C(h)],r&&(a==="click"||a==="preclick")&&this._draggableMoved(r)){_=!0;break}if(r&&r.listens(a,!0)&&(c&&!$i(h,i)||(s.push(r),c))||h===this._container)break;h=h.parentNode}return!s.length&&!_&&!c&&this.listens(a,!0)&&(s=[this]),s},_isClickDisabled:function(i){for(;i&&i!==this._container;){if(i._leaflet_disable_click)return!0;i=i.parentNode}},_handleDOMEvent:function(i){var a=i.target||i.srcElement;if(!(!this._loaded||a._leaflet_disable_events||i.type==="click"&&this._isClickDisabled(a))){var s=i.type;s==="mousedown"&&ba(a),this._fireDOMEvent(i,s)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(i,a,s){if(i.type==="click"){var r=N({},i);r.type="preclick",this._fireDOMEvent(r,r.type,s)}var c=this._findEventTargets(i,a);if(s){for(var h=[],_=0;_<s.length;_++)s[_].listens(a,!0)&&h.push(s[_]);c=h.concat(c)}if(c.length){a==="contextmenu"&&Qt(i);var T=c[0],O={originalEvent:i};if(i.type!=="keypress"&&i.type!=="keydown"&&i.type!=="keyup"){var H=T.getLatLng&&(!T._radius||T._radius<=10);O.containerPoint=H?this.latLngToContainerPoint(T.getLatLng()):this.mouseEventToContainerPoint(i),O.layerPoint=this.containerPointToLayerPoint(O.containerPoint),O.latlng=H?T.getLatLng():this.layerPointToLatLng(O.layerPoint)}for(_=0;_<c.length;_++)if(c[_].fire(a,O,!0),O.originalEvent._stopped||c[_].options.bubblingMouseEvents===!1&&fe(this._mouseEvents,a)!==-1)return}},_draggableMoved:function(i){return i=i.dragging&&i.dragging.enabled()?i:this,i.dragging&&i.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var i=0,a=this._handlers.length;i<a;i++)this._handlers[i].disable()},whenReady:function(i,a){return this._loaded?i.call(a||this,{target:this}):this.on("load",i,a),this},_getMapPanePos:function(){return Li(this._mapPane)||new P(0,0)},_moved:function(){var i=this._getMapPanePos();return i&&!i.equals([0,0])},_getTopLeftPoint:function(i,a){var s=i&&a!==void 0?this._getNewPixelOrigin(i,a):this.getPixelOrigin();return s.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(i,a){var s=this.getSize()._divideBy(2);return this.project(i,a)._subtract(s)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(i,a,s){var r=this._getNewPixelOrigin(s,a);return this.project(i,a)._subtract(r)},_latLngBoundsToNewLayerBounds:function(i,a,s){var r=this._getNewPixelOrigin(s,a);return V([this.project(i.getSouthWest(),a)._subtract(r),this.project(i.getNorthWest(),a)._subtract(r),this.project(i.getSouthEast(),a)._subtract(r),this.project(i.getNorthEast(),a)._subtract(r)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(i){return this.latLngToLayerPoint(i).subtract(this._getCenterLayerPoint())},_limitCenter:function(i,a,s){if(!s)return i;var r=this.project(i,a),c=this.getSize().divideBy(2),h=new R(r.subtract(c),r.add(c)),_=this._getBoundsOffset(h,s,a);return Math.abs(_.x)<=1&&Math.abs(_.y)<=1?i:this.unproject(r.add(_),a)},_limitOffset:function(i,a){if(!a)return i;var s=this.getPixelBounds(),r=new R(s.min.add(i),s.max.add(i));return i.add(this._getBoundsOffset(r,a))},_getBoundsOffset:function(i,a,s){var r=V(this.project(a.getNorthEast(),s),this.project(a.getSouthWest(),s)),c=r.min.subtract(i.min),h=r.max.subtract(i.max),_=this._rebound(c.x,-h.x),T=this._rebound(c.y,-h.y);return new P(_,T)},_rebound:function(i,a){return i+a>0?Math.round(i-a)/2:Math.max(0,Math.ceil(i))-Math.max(0,Math.floor(a))},_limitZoom:function(i){var a=this.getMinZoom(),s=this.getMaxZoom(),r=et.any3d?this.options.zoomSnap:1;return r&&(i=Math.round(i/r)*r),Math.max(a,Math.min(s,i))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Rt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(i,a){var s=this._getCenterOffset(i)._trunc();return(a&&a.animate)!==!0&&!this.getSize().contains(s)?!1:(this.panBy(s,a),!0)},_createAnimProxy:function(){var i=this._proxy=yt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(i),this.on("zoomanim",function(a){var s=ga,r=this._proxy.style[s];qe(this._proxy,this.project(a.center,a.zoom),this.getZoomScale(a.zoom,1)),r===this._proxy.style[s]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Dt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var i=this.getCenter(),a=this.getZoom();qe(this._proxy,this.project(i,a),this.getZoomScale(a,1))},_catchTransitionEnd:function(i){this._animatingZoom&&i.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(i,a,s){if(this._animatingZoom)return!0;if(s=s||{},!this._zoomAnimated||s.animate===!1||this._nothingToAnimate()||Math.abs(a-this._zoom)>this.options.zoomAnimationThreshold)return!1;var r=this.getZoomScale(a),c=this._getCenterOffset(i)._divideBy(1-1/r);return s.animate!==!0&&!this.getSize().contains(c)?!1:(jt(function(){this._moveStart(!0,s.noMoveStart||!1)._animateZoom(i,a,!0)},this),!0)},_animateZoom:function(i,a,s,r){this._mapPane&&(s&&(this._animatingZoom=!0,this._animateToCenter=i,this._animateToZoom=a,rt(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:i,zoom:a,noUpdate:r}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(G(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Rt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Ma(i,a){return new _t(i,a)}var we=de.extend({options:{position:"topright"},initialize:function(i){mt(this,i)},getPosition:function(){return this.options.position},setPosition:function(i){var a=this._map;return a&&a.removeControl(this),this.options.position=i,a&&a.addControl(this),this},getContainer:function(){return this._container},addTo:function(i){this.remove(),this._map=i;var a=this._container=this.onAdd(i),s=this.getPosition(),r=i._controlCorners[s];return rt(a,"leaflet-control"),s.indexOf("bottom")!==-1?r.insertBefore(a,r.firstChild):r.appendChild(a),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Dt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(i){this._map&&i&&i.screenX>0&&i.screenY>0&&this._map.getContainer().focus()}}),Rn=function(i){return new we(i)};_t.include({addControl:function(i){return i.addTo(this),this},removeControl:function(i){return i.remove(),this},_initControlPos:function(){var i=this._controlCorners={},a="leaflet-",s=this._controlContainer=yt("div",a+"control-container",this._container);function r(c,h){var _=a+c+" "+a+h;i[c+h]=yt("div",_,s)}r("top","left"),r("top","right"),r("bottom","left"),r("bottom","right")},_clearControlPos:function(){for(var i in this._controlCorners)Dt(this._controlCorners[i]);Dt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var ao=we.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(i,a,s,r){return s<r?-1:r<s?1:0}},initialize:function(i,a,s){mt(this,s),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var r in i)this._addLayer(i[r],r);for(r in a)this._addLayer(a[r],r,!0)},onAdd:function(i){this._initLayout(),this._update(),this._map=i,i.on("zoomend",this._checkDisabledLayers,this);for(var a=0;a<this._layers.length;a++)this._layers[a].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(i){return we.prototype.addTo.call(this,i),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(i,a){return this._addLayer(i,a),this._map?this._update():this},addOverlay:function(i,a){return this._addLayer(i,a,!0),this._map?this._update():this},removeLayer:function(i){i.off("add remove",this._onLayerChange,this);var a=this._getLayer(C(i));return a&&this._layers.splice(this._layers.indexOf(a),1),this._map?this._update():this},expand:function(){rt(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var i=this._map.getSize().y-(this._container.offsetTop+50);return i<this._section.clientHeight?(rt(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=i+"px"):Rt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Rt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var i="leaflet-control-layers",a=this._container=yt("div",i),s=this.options.collapsed;a.setAttribute("aria-haspopup",!0),Dn(a),Cn(a);var r=this._section=yt("section",i+"-list");s&&(this._map.on("click",this.collapse,this),ut(a,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=yt("a",i+"-toggle",a);c.href="#",c.title="Layers",c.setAttribute("role","button"),ut(c,{keydown:function(h){h.keyCode===13&&this._expandSafely()},click:function(h){Qt(h),this._expandSafely()}},this),s||this.expand(),this._baseLayersList=yt("div",i+"-base",r),this._separator=yt("div",i+"-separator",r),this._overlaysList=yt("div",i+"-overlays",r),a.appendChild(r)},_getLayer:function(i){for(var a=0;a<this._layers.length;a++)if(this._layers[a]&&C(this._layers[a].layer)===i)return this._layers[a]},_addLayer:function(i,a,s){this._map&&i.on("add remove",this._onLayerChange,this),this._layers.push({layer:i,name:a,overlay:s}),this.options.sortLayers&&this._layers.sort(G(function(r,c){return this.options.sortFunction(r.layer,c.layer,r.name,c.name)},this)),this.options.autoZIndex&&i.setZIndex&&(this._lastZIndex++,i.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;me(this._baseLayersList),me(this._overlaysList),this._layerControlInputs=[];var i,a,s,r,c=0;for(s=0;s<this._layers.length;s++)r=this._layers[s],this._addItem(r),a=a||r.overlay,i=i||!r.overlay,c+=r.overlay?0:1;return this.options.hideSingleBase&&(i=i&&c>1,this._baseLayersList.style.display=i?"":"none"),this._separator.style.display=a&&i?"":"none",this},_onLayerChange:function(i){this._handlingClick||this._update();var a=this._getLayer(C(i.target)),s=a.overlay?i.type==="add"?"overlayadd":"overlayremove":i.type==="add"?"baselayerchange":null;s&&this._map.fire(s,a)},_createRadioElement:function(i,a){var s='<input type="radio" class="leaflet-control-layers-selector" name="'+i+'"'+(a?' checked="checked"':"")+"/>",r=document.createElement("div");return r.innerHTML=s,r.firstChild},_addItem:function(i){var a=document.createElement("label"),s=this._map.hasLayer(i.layer),r;i.overlay?(r=document.createElement("input"),r.type="checkbox",r.className="leaflet-control-layers-selector",r.defaultChecked=s):r=this._createRadioElement("leaflet-base-layers_"+C(this),s),this._layerControlInputs.push(r),r.layerId=C(i.layer),ut(r,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+i.name;var h=document.createElement("span");a.appendChild(h),h.appendChild(r),h.appendChild(c);var _=i.overlay?this._overlaysList:this._baseLayersList;return _.appendChild(a),this._checkDisabledLayers(),a},_onInputClick:function(){if(!this._preventClick){var i=this._layerControlInputs,a,s,r=[],c=[];this._handlingClick=!0;for(var h=i.length-1;h>=0;h--)a=i[h],s=this._getLayer(a.layerId).layer,a.checked?r.push(s):a.checked||c.push(s);for(h=0;h<c.length;h++)this._map.hasLayer(c[h])&&this._map.removeLayer(c[h]);for(h=0;h<r.length;h++)this._map.hasLayer(r[h])||this._map.addLayer(r[h]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var i=this._layerControlInputs,a,s,r=this._map.getZoom(),c=i.length-1;c>=0;c--)a=i[c],s=this._getLayer(a.layerId).layer,a.disabled=s.options.minZoom!==void 0&&r<s.options.minZoom||s.options.maxZoom!==void 0&&r>s.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var i=this._section;this._preventClick=!0,ut(i,"click",Qt),this.expand();var a=this;setTimeout(function(){St(i,"click",Qt),a._preventClick=!1})}}),zu=function(i,a,s){return new ao(i,a,s)},Xl=we.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(i){var a="leaflet-control-zoom",s=yt("div",a+" leaflet-bar"),r=this.options;return this._zoomInButton=this._createButton(r.zoomInText,r.zoomInTitle,a+"-in",s,this._zoomIn),this._zoomOutButton=this._createButton(r.zoomOutText,r.zoomOutTitle,a+"-out",s,this._zoomOut),this._updateDisabled(),i.on("zoomend zoomlevelschange",this._updateDisabled,this),s},onRemove:function(i){i.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(i){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(i.shiftKey?3:1))},_zoomOut:function(i){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(i.shiftKey?3:1))},_createButton:function(i,a,s,r,c){var h=yt("a",s,r);return h.innerHTML=i,h.href="#",h.title=a,h.setAttribute("role","button"),h.setAttribute("aria-label",a),Dn(h),ut(h,"click",oi),ut(h,"click",c,this),ut(h,"click",this._refocusOnMap,this),h},_updateDisabled:function(){var i=this._map,a="leaflet-disabled";Rt(this._zoomInButton,a),Rt(this._zoomOutButton,a),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||i._zoom===i.getMinZoom())&&(rt(this._zoomOutButton,a),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||i._zoom===i.getMaxZoom())&&(rt(this._zoomInButton,a),this._zoomInButton.setAttribute("aria-disabled","true"))}});_t.mergeOptions({zoomControl:!0}),_t.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Xl,this.addControl(this.zoomControl))});var Ql=function(i){return new Xl(i)},lo=we.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(i){var a="leaflet-control-scale",s=yt("div",a),r=this.options;return this._addScales(r,a+"-line",s),i.on(r.updateWhenIdle?"moveend":"move",this._update,this),i.whenReady(this._update,this),s},onRemove:function(i){i.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(i,a,s){i.metric&&(this._mScale=yt("div",a,s)),i.imperial&&(this._iScale=yt("div",a,s))},_update:function(){var i=this._map,a=i.getSize().y/2,s=i.distance(i.containerPointToLatLng([0,a]),i.containerPointToLatLng([this.options.maxWidth,a]));this._updateScales(s)},_updateScales:function(i){this.options.metric&&i&&this._updateMetric(i),this.options.imperial&&i&&this._updateImperial(i)},_updateMetric:function(i){var a=this._getRoundNum(i),s=a<1e3?a+" m":a/1e3+" km";this._updateScale(this._mScale,s,a/i)},_updateImperial:function(i){var a=i*3.2808399,s,r,c;a>5280?(s=a/5280,r=this._getRoundNum(s),this._updateScale(this._iScale,r+" mi",r/s)):(c=this._getRoundNum(a),this._updateScale(this._iScale,c+" ft",c/a))},_updateScale:function(i,a,s){i.style.width=Math.round(this.options.maxWidth*s)+"px",i.innerHTML=a},_getRoundNum:function(i){var a=Math.pow(10,(Math.floor(i)+"").length-1),s=i/a;return s=s>=10?10:s>=5?5:s>=3?3:s>=2?2:1,a*s}}),Lu=function(i){return new lo(i)},Au='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Kl=we.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(et.inlineSvg?Au+" ":"")+"Leaflet</a>"},initialize:function(i){mt(this,i),this._attributions={}},onAdd:function(i){i.attributionControl=this,this._container=yt("div","leaflet-control-attribution"),Dn(this._container);for(var a in i._layers)i._layers[a].getAttribution&&this.addAttribution(i._layers[a].getAttribution());return this._update(),i.on("layeradd",this._addAttribution,this),this._container},onRemove:function(i){i.off("layeradd",this._addAttribution,this)},_addAttribution:function(i){i.layer.getAttribution&&(this.addAttribution(i.layer.getAttribution()),i.layer.once("remove",function(){this.removeAttribution(i.layer.getAttribution())},this))},setPrefix:function(i){return this.options.prefix=i,this._update(),this},addAttribution:function(i){return i?(this._attributions[i]||(this._attributions[i]=0),this._attributions[i]++,this._update(),this):this},removeAttribution:function(i){return i?(this._attributions[i]&&(this._attributions[i]--,this._update()),this):this},_update:function(){if(this._map){var i=[];for(var a in this._attributions)this._attributions[a]&&i.push(a);var s=[];this.options.prefix&&s.push(this.options.prefix),i.length&&s.push(i.join(", ")),this._container.innerHTML=s.join(' <span aria-hidden="true">|</span> ')}}});_t.mergeOptions({attributionControl:!0}),_t.addInitHook(function(){this.options.attributionControl&&new Kl().addTo(this)});var Ou=function(i){return new Kl(i)};we.Layers=ao,we.Zoom=Xl,we.Scale=lo,we.Attribution=Kl,Rn.layers=zu,Rn.zoom=Ql,Rn.scale=Lu,Rn.attribution=Ou;var Xe=de.extend({initialize:function(i){this._map=i},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});Xe.addTo=function(i,a){return i.addHandler(a,this),this};var Nu={Events:D},so=et.touch?"touchstart mousedown":"mousedown",Oi=Q.extend({options:{clickTolerance:3},initialize:function(i,a,s,r){mt(this,r),this._element=i,this._dragStartTarget=a||i,this._preventOutline=s},enable:function(){this._enabled||(ut(this._dragStartTarget,so,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Oi._dragging===this&&this.finishDrag(!0),St(this._dragStartTarget,so,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(i){if(this._enabled&&(this._moved=!1,!zn(this._element,"leaflet-zoom-anim"))){if(i.touches&&i.touches.length!==1){Oi._dragging===this&&this.finishDrag();return}if(!(Oi._dragging||i.shiftKey||i.which!==1&&i.button!==1&&!i.touches)&&(Oi._dragging=this,this._preventOutline&&ba(this._element),xa(),ai(),!this._moving)){this.fire("down");var a=i.touches?i.touches[0]:i,s=Wi(this._element);this._startPoint=new P(a.clientX,a.clientY),this._startPos=Li(this._element),this._parentScale=Ai(s);var r=i.type==="mousedown";ut(document,r?"mousemove":"touchmove",this._onMove,this),ut(document,r?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(i){if(this._enabled){if(i.touches&&i.touches.length>1){this._moved=!0;return}var a=i.touches&&i.touches.length===1?i.touches[0]:i,s=new P(a.clientX,a.clientY)._subtract(this._startPoint);!s.x&&!s.y||Math.abs(s.x)+Math.abs(s.y)<this.options.clickTolerance||(s.x/=this._parentScale.x,s.y/=this._parentScale.y,Qt(i),this._moved||(this.fire("dragstart"),this._moved=!0,rt(document.body,"leaflet-dragging"),this._lastTarget=i.target||i.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),rt(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(s),this._moving=!0,this._lastEvent=i,this._updatePosition())}},_updatePosition:function(){var i={originalEvent:this._lastEvent};this.fire("predrag",i),Gt(this._element,this._newPos),this.fire("drag",i)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(i){Rt(document.body,"leaflet-dragging"),this._lastTarget&&(Rt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),St(document,"mousemove touchmove",this._onMove,this),St(document,"mouseup touchend touchcancel",this._onUp,this),Yl(),ya();var a=this._moved&&this._moving;this._moving=!1,Oi._dragging=!1,a&&this.fire("dragend",{noInertia:i,distance:this._newPos.distanceTo(this._startPos)})}});function oo(i,a,s){var r,c=[1,4,2,8],h,_,T,O,H,K,W,$;for(h=0,K=i.length;h<K;h++)i[h]._code=tn(i[h],a);for(T=0;T<4;T++){for(W=c[T],r=[],h=0,K=i.length,_=K-1;h<K;_=h++)O=i[h],H=i[_],O._code&W?H._code&W||($=il(H,O,W,a,s),$._code=tn($,a),r.push($)):(H._code&W&&($=il(H,O,W,a,s),$._code=tn($,a),r.push($)),r.push(O));i=r}return i}function el(i,a){var s,r,c,h,_,T,O,H,K;if(!i||i.length===0)throw new Error("latlngs not passed");se(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var W=X([0,0]),$=J(i),ot=$.getNorthWest().distanceTo($.getSouthWest())*$.getNorthEast().distanceTo($.getNorthWest());ot<1700&&(W=Jl(i));var Bt=i.length,Yt=[];for(s=0;s<Bt;s++){var oe=X(i[s]);Yt.push(a.project(X([oe.lat-W.lat,oe.lng-W.lng])))}for(T=O=H=0,s=0,r=Bt-1;s<Bt;r=s++)c=Yt[s],h=Yt[r],_=c.y*h.x-h.y*c.x,O+=(c.x+h.x)*_,H+=(c.y+h.y)*_,T+=_*3;T===0?K=Yt[0]:K=[O/T,H/T];var Pe=a.unproject(m(K));return X([Pe.lat+W.lat,Pe.lng+W.lng])}function Jl(i){for(var a=0,s=0,r=0,c=0;c<i.length;c++){var h=X(i[c]);a+=h.lat,s+=h.lng,r++}return X([a/r,s/r])}var Cu={__proto__:null,clipPolygon:oo,polygonCenter:el,centroid:Jl};function uo(i,a){if(!a||!i.length)return i.slice();var s=a*a;return i=Bu(i,s),i=Ru(i,s),i}function Wl(i,a,s){return Math.sqrt(en(i,a,s,!0))}function Du(i,a,s){return en(i,a,s)}function Ru(i,a){var s=i.length,r=typeof Uint8Array<"u"?Uint8Array:Array,c=new r(s);c[0]=c[s-1]=1,Fl(i,c,a,0,s-1);var h,_=[];for(h=0;h<s;h++)c[h]&&_.push(i[h]);return _}function Fl(i,a,s,r,c){var h=0,_,T,O;for(T=r+1;T<=c-1;T++)O=en(i[T],i[r],i[c],!0),O>h&&(_=T,h=O);h>s&&(a[_]=1,Fl(i,a,s,r,_),Fl(i,a,s,_,c))}function Bu(i,a){for(var s=[i[0]],r=1,c=0,h=i.length;r<h;r++)Zu(i[r],i[c])>a&&(s.push(i[r]),c=r);return c<h-1&&s.push(i[h-1]),s}var ro;function co(i,a,s,r,c){var h=r?ro:tn(i,s),_=tn(a,s),T,O,H;for(ro=_;;){if(!(h|_))return[i,a];if(h&_)return!1;T=h||_,O=il(i,a,T,s,c),H=tn(O,s),T===h?(i=O,h=H):(a=O,_=H)}}function il(i,a,s,r,c){var h=a.x-i.x,_=a.y-i.y,T=r.min,O=r.max,H,K;return s&8?(H=i.x+h*(O.y-i.y)/_,K=O.y):s&4?(H=i.x+h*(T.y-i.y)/_,K=T.y):s&2?(H=O.x,K=i.y+_*(O.x-i.x)/h):s&1&&(H=T.x,K=i.y+_*(T.x-i.x)/h),new P(H,K,c)}function tn(i,a){var s=0;return i.x<a.min.x?s|=1:i.x>a.max.x&&(s|=2),i.y<a.min.y?s|=4:i.y>a.max.y&&(s|=8),s}function Zu(i,a){var s=a.x-i.x,r=a.y-i.y;return s*s+r*r}function en(i,a,s,r){var c=a.x,h=a.y,_=s.x-c,T=s.y-h,O=_*_+T*T,H;return O>0&&(H=((i.x-c)*_+(i.y-h)*T)/O,H>1?(c=s.x,h=s.y):H>0&&(c+=_*H,h+=T*H)),_=i.x-c,T=i.y-h,r?_*_+T*T:new P(c,h)}function se(i){return!Mt(i[0])||typeof i[0][0]!="object"&&typeof i[0][0]<"u"}function fo(i){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),se(i)}function Il(i,a){var s,r,c,h,_,T,O,H;if(!i||i.length===0)throw new Error("latlngs not passed");se(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var K=X([0,0]),W=J(i),$=W.getNorthWest().distanceTo(W.getSouthWest())*W.getNorthEast().distanceTo(W.getNorthWest());$<1700&&(K=Jl(i));var ot=i.length,Bt=[];for(s=0;s<ot;s++){var Yt=X(i[s]);Bt.push(a.project(X([Yt.lat-K.lat,Yt.lng-K.lng])))}for(s=0,r=0;s<ot-1;s++)r+=Bt[s].distanceTo(Bt[s+1])/2;if(r===0)H=Bt[0];else for(s=0,h=0;s<ot-1;s++)if(_=Bt[s],T=Bt[s+1],c=_.distanceTo(T),h+=c,h>r){O=(h-r)/c,H=[T.x-O*(T.x-_.x),T.y-O*(T.y-_.y)];break}var oe=a.unproject(m(H));return X([oe.lat+K.lat,oe.lng+K.lng])}var ho={__proto__:null,simplify:uo,pointToSegmentDistance:Wl,closestPointOnSegment:Du,clipSegment:co,_getEdgeIntersection:il,_getBitCode:tn,_sqClosestPointOnSegment:en,isFlat:se,_flat:fo,polylineCenter:Il},nl={project:function(i){return new P(i.lng,i.lat)},unproject:function(i){return new I(i.y,i.x)},bounds:new R([-180,-90],[180,90])},al={R:6378137,R_MINOR:6356752314245179e-9,bounds:new R([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(i){var a=Math.PI/180,s=this.R,r=i.lat*a,c=this.R_MINOR/s,h=Math.sqrt(1-c*c),_=h*Math.sin(r),T=Math.tan(Math.PI/4-r/2)/Math.pow((1-_)/(1+_),h/2);return r=-s*Math.log(Math.max(T,1e-10)),new P(i.lng*a*s,r)},unproject:function(i){for(var a=180/Math.PI,s=this.R,r=this.R_MINOR/s,c=Math.sqrt(1-r*r),h=Math.exp(-i.y/s),_=Math.PI/2-2*Math.atan(h),T=0,O=.1,H;T<15&&Math.abs(O)>1e-7;T++)H=c*Math.sin(_),H=Math.pow((1-H)/(1+H),c/2),O=Math.PI/2-2*Math.atan(h*H)-_,_+=O;return new I(_*a,i.x*a/s)}},mo={__proto__:null,LonLat:nl,Mercator:al,SphericalMercator:ma},Bn=N({},bt,{code:"EPSG:3395",projection:al,transformation:function(){var i=.5/(Math.PI*al.R);return Pi(i,.5,-i,.5)}()}),_o=N({},bt,{code:"EPSG:4326",projection:nl,transformation:Pi(1/180,1,-1/180,.5)}),Uu=N({},kt,{projection:nl,transformation:Pi(1,0,-1,0),scale:function(i){return Math.pow(2,i)},zoom:function(i){return Math.log(i)/Math.LN2},distance:function(i,a){var s=a.lng-i.lng,r=a.lat-i.lat;return Math.sqrt(s*s+r*r)},infinite:!0});kt.Earth=bt,kt.EPSG3395=Bn,kt.EPSG3857=_a,kt.EPSG900913=Bl,kt.EPSG4326=_o,kt.Simple=Uu;var Qe=Q.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(i){return i.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(i){return i&&i.removeLayer(this),this},getPane:function(i){return this._map.getPane(i?this.options[i]||i:this.options.pane)},addInteractiveTarget:function(i){return this._map._targets[C(i)]=this,this},removeInteractiveTarget:function(i){return delete this._map._targets[C(i)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(i){var a=i.target;if(a.hasLayer(this)){if(this._map=a,this._zoomAnimated=a._zoomAnimated,this.getEvents){var s=this.getEvents();a.on(s,this),this.once("remove",function(){a.off(s,this)},this)}this.onAdd(a),this.fire("add"),a.fire("layeradd",{layer:this})}}});_t.include({addLayer:function(i){if(!i._layerAdd)throw new Error("The provided object is not a Layer.");var a=C(i);return this._layers[a]?this:(this._layers[a]=i,i._mapToAdd=this,i.beforeAdd&&i.beforeAdd(this),this.whenReady(i._layerAdd,i),this)},removeLayer:function(i){var a=C(i);return this._layers[a]?(this._loaded&&i.onRemove(this),delete this._layers[a],this._loaded&&(this.fire("layerremove",{layer:i}),i.fire("remove")),i._map=i._mapToAdd=null,this):this},hasLayer:function(i){return C(i)in this._layers},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},_addLayers:function(i){i=i?Mt(i)?i:[i]:[];for(var a=0,s=i.length;a<s;a++)this.addLayer(i[a])},_addZoomLimit:function(i){(!isNaN(i.options.maxZoom)||!isNaN(i.options.minZoom))&&(this._zoomBoundLayers[C(i)]=i,this._updateZoomLevels())},_removeZoomLimit:function(i){var a=C(i);this._zoomBoundLayers[a]&&(delete this._zoomBoundLayers[a],this._updateZoomLevels())},_updateZoomLevels:function(){var i=1/0,a=-1/0,s=this._getZoomSpan();for(var r in this._zoomBoundLayers){var c=this._zoomBoundLayers[r].options;i=c.minZoom===void 0?i:Math.min(i,c.minZoom),a=c.maxZoom===void 0?a:Math.max(a,c.maxZoom)}this._layersMaxZoom=a===-1/0?void 0:a,this._layersMinZoom=i===1/0?void 0:i,s!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var nn=Qe.extend({initialize:function(i,a){mt(this,a),this._layers={};var s,r;if(i)for(s=0,r=i.length;s<r;s++)this.addLayer(i[s])},addLayer:function(i){var a=this.getLayerId(i);return this._layers[a]=i,this._map&&this._map.addLayer(i),this},removeLayer:function(i){var a=i in this._layers?i:this.getLayerId(i);return this._map&&this._layers[a]&&this._map.removeLayer(this._layers[a]),delete this._layers[a],this},hasLayer:function(i){var a=typeof i=="number"?i:this.getLayerId(i);return a in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(i){var a=Array.prototype.slice.call(arguments,1),s,r;for(s in this._layers)r=this._layers[s],r[i]&&r[i].apply(r,a);return this},onAdd:function(i){this.eachLayer(i.addLayer,i)},onRemove:function(i){this.eachLayer(i.removeLayer,i)},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},getLayer:function(i){return this._layers[i]},getLayers:function(){var i=[];return this.eachLayer(i.push,i),i},setZIndex:function(i){return this.invoke("setZIndex",i)},getLayerId:function(i){return C(i)}}),vo=function(i,a){return new nn(i,a)},Ne=nn.extend({addLayer:function(i){return this.hasLayer(i)?this:(i.addEventParent(this),nn.prototype.addLayer.call(this,i),this.fire("layeradd",{layer:i}))},removeLayer:function(i){return this.hasLayer(i)?(i in this._layers&&(i=this._layers[i]),i.removeEventParent(this),nn.prototype.removeLayer.call(this,i),this.fire("layerremove",{layer:i})):this},setStyle:function(i){return this.invoke("setStyle",i)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var i=new k;for(var a in this._layers){var s=this._layers[a];i.extend(s.getBounds?s.getBounds():s.getLatLng())}return i}}),wa=function(i,a){return new Ne(i,a)},Zn=de.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(i){mt(this,i)},createIcon:function(i){return this._createIcon("icon",i)},createShadow:function(i){return this._createIcon("shadow",i)},_createIcon:function(i,a){var s=this._getIconUrl(i);if(!s){if(i==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var r=this._createImg(s,a&&a.tagName==="IMG"?a:null);return this._setIconStyles(r,i),(this.options.crossOrigin||this.options.crossOrigin==="")&&(r.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),r},_setIconStyles:function(i,a){var s=this.options,r=s[a+"Size"];typeof r=="number"&&(r=[r,r]);var c=m(r),h=m(a==="shadow"&&s.shadowAnchor||s.iconAnchor||c&&c.divideBy(2,!0));i.className="leaflet-marker-"+a+" "+(s.className||""),h&&(i.style.marginLeft=-h.x+"px",i.style.marginTop=-h.y+"px"),c&&(i.style.width=c.x+"px",i.style.height=c.y+"px")},_createImg:function(i,a){return a=a||document.createElement("img"),a.src=i,a},_getIconUrl:function(i){return et.retina&&this.options[i+"RetinaUrl"]||this.options[i+"Url"]}});function ll(i){return new Zn(i)}var Un=Zn.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(i){return typeof Un.imagePath!="string"&&(Un.imagePath=this._detectIconPath()),(this.options.imagePath||Un.imagePath)+Zn.prototype._getIconUrl.call(this,i)},_stripUrl:function(i){var a=function(s,r,c){var h=r.exec(s);return h&&h[c]};return i=a(i,/^url\((['"])?(.+)\1\)$/,2),i&&a(i,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var i=yt("div","leaflet-default-icon-path",document.body),a=Ki(i,"background-image")||Ki(i,"backgroundImage");if(document.body.removeChild(i),a=this._stripUrl(a),a)return a;var s=document.querySelector('link[href$="leaflet.css"]');return s?s.href.substring(0,s.href.length-11-1):""}}),$l=Xe.extend({initialize:function(i){this._marker=i},addHooks:function(){var i=this._marker._icon;this._draggable||(this._draggable=new Oi(i,i,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),rt(i,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Rt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(i){var a=this._marker,s=a._map,r=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,h=Li(a._icon),_=s.getPixelBounds(),T=s.getPixelOrigin(),O=V(_.min._subtract(T).add(c),_.max._subtract(T).subtract(c));if(!O.contains(h)){var H=m((Math.max(O.max.x,h.x)-O.max.x)/(_.max.x-O.max.x)-(Math.min(O.min.x,h.x)-O.min.x)/(_.min.x-O.min.x),(Math.max(O.max.y,h.y)-O.max.y)/(_.max.y-O.max.y)-(Math.min(O.min.y,h.y)-O.min.y)/(_.min.y-O.min.y)).multiplyBy(r);s.panBy(H,{animate:!1}),this._draggable._newPos._add(H),this._draggable._startPos._add(H),Gt(a._icon,this._draggable._newPos),this._onDrag(i),this._panRequest=jt(this._adjustPan.bind(this,i))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(i){this._marker.options.autoPan&&(Et(this._panRequest),this._panRequest=jt(this._adjustPan.bind(this,i)))},_onDrag:function(i){var a=this._marker,s=a._shadow,r=Li(a._icon),c=a._map.layerPointToLatLng(r);s&&Gt(s,r),a._latlng=c,i.latlng=c,i.oldLatLng=this._oldLatLng,a.fire("move",i).fire("drag",i)},_onDragEnd:function(i){Et(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",i)}}),jn=Qe.extend({options:{icon:new Un,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(i,a){mt(this,a),this._latlng=X(i)},onAdd:function(i){this._zoomAnimated=this._zoomAnimated&&i.options.markerZoomAnimation,this._zoomAnimated&&i.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(i){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&i.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.update(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},setZIndexOffset:function(i){return this.options.zIndexOffset=i,this.update()},getIcon:function(){return this.options.icon},setIcon:function(i){return this.options.icon=i,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var i=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(i)}return this},_initIcon:function(){var i=this.options,a="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),s=i.icon.createIcon(this._icon),r=!1;s!==this._icon&&(this._icon&&this._removeIcon(),r=!0,i.title&&(s.title=i.title),s.tagName==="IMG"&&(s.alt=i.alt||"")),rt(s,a),i.keyboard&&(s.tabIndex="0",s.setAttribute("role","button")),this._icon=s,i.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&ut(s,"focus",this._panOnFocus,this);var c=i.icon.createShadow(this._shadow),h=!1;c!==this._shadow&&(this._removeShadow(),h=!0),c&&(rt(c,a),c.alt=""),this._shadow=c,i.opacity<1&&this._updateOpacity(),r&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&h&&this.getPane(i.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&St(this._icon,"focus",this._panOnFocus,this),Dt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Dt(this._shadow),this._shadow=null},_setPos:function(i){this._icon&&Gt(this._icon,i),this._shadow&&Gt(this._shadow,i),this._zIndex=i.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(i){this._icon&&(this._icon.style.zIndex=this._zIndex+i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center).round();this._setPos(a)},_initInteraction:function(){if(this.options.interactive&&(rt(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),$l)){var i=this.options.draggable;this.dragging&&(i=this.dragging.enabled(),this.dragging.disable()),this.dragging=new $l(this),i&&this.dragging.enable()}},setOpacity:function(i){return this.options.opacity=i,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var i=this.options.opacity;this._icon&&Me(this._icon,i),this._shadow&&Me(this._shadow,i)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var i=this._map;if(i){var a=this.options.icon.options,s=a.iconSize?m(a.iconSize):m(0,0),r=a.iconAnchor?m(a.iconAnchor):m(0,0);i.panInside(this._latlng,{paddingTopLeft:r,paddingBottomRight:s.subtract(r)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function ts(i,a){return new jn(i,a)}var yi=Qe.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(i){this._renderer=i.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(i){return mt(this,i),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&i&&Object.prototype.hasOwnProperty.call(i,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Ea=yi.extend({options:{fill:!0,radius:10},initialize:function(i,a){mt(this,a),this._latlng=X(i),this._radius=this.options.radius},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.redraw(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(i){return this.options.radius=this._radius=i,this.redraw()},getRadius:function(){return this._radius},setStyle:function(i){var a=i&&i.radius||this._radius;return yi.prototype.setStyle.call(this,i),this.setRadius(a),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var i=this._radius,a=this._radiusY||i,s=this._clickTolerance(),r=[i+s,a+s];this._pxBounds=new R(this._point.subtract(r),this._point.add(r))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(i){return i.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function po(i,a){return new Ea(i,a)}var es=Ea.extend({initialize:function(i,a,s){if(typeof a=="number"&&(a=N({},s,{radius:a})),mt(this,a),this._latlng=X(i),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(i){return this._mRadius=i,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var i=[this._radius,this._radiusY||this._radius];return new k(this._map.layerPointToLatLng(this._point.subtract(i)),this._map.layerPointToLatLng(this._point.add(i)))},setStyle:yi.prototype.setStyle,_project:function(){var i=this._latlng.lng,a=this._latlng.lat,s=this._map,r=s.options.crs;if(r.distance===bt.distance){var c=Math.PI/180,h=this._mRadius/bt.R/c,_=s.project([a+h,i]),T=s.project([a-h,i]),O=_.add(T).divideBy(2),H=s.unproject(O).lat,K=Math.acos((Math.cos(h*c)-Math.sin(a*c)*Math.sin(H*c))/(Math.cos(a*c)*Math.cos(H*c)))/c;(isNaN(K)||K===0)&&(K=h/Math.cos(Math.PI/180*a)),this._point=O.subtract(s.getPixelOrigin()),this._radius=isNaN(K)?0:O.x-s.project([H,i-K]).x,this._radiusY=O.y-_.y}else{var W=r.unproject(r.project(this._latlng).subtract([this._mRadius,0]));this._point=s.latLngToLayerPoint(this._latlng),this._radius=this._point.x-s.latLngToLayerPoint(W).x}this._updateBounds()}});function ju(i,a,s){return new es(i,a,s)}var xi=yi.extend({options:{smoothFactor:1,noClip:!1},initialize:function(i,a){mt(this,a),this._setLatLngs(i)},getLatLngs:function(){return this._latlngs},setLatLngs:function(i){return this._setLatLngs(i),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(i){for(var a=1/0,s=null,r=en,c,h,_=0,T=this._parts.length;_<T;_++)for(var O=this._parts[_],H=1,K=O.length;H<K;H++){c=O[H-1],h=O[H];var W=r(i,c,h,!0);W<a&&(a=W,s=r(i,c,h))}return s&&(s.distance=Math.sqrt(a)),s},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Il(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(i,a){return a=a||this._defaultShape(),i=X(i),a.push(i),this._bounds.extend(i),this.redraw()},_setLatLngs:function(i){this._bounds=new k,this._latlngs=this._convertLatLngs(i)},_defaultShape:function(){return se(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(i){for(var a=[],s=se(i),r=0,c=i.length;r<c;r++)s?(a[r]=X(i[r]),this._bounds.extend(a[r])):a[r]=this._convertLatLngs(i[r]);return a},_project:function(){var i=new R;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,i),this._bounds.isValid()&&i.isValid()&&(this._rawPxBounds=i,this._updateBounds())},_updateBounds:function(){var i=this._clickTolerance(),a=new P(i,i);this._rawPxBounds&&(this._pxBounds=new R([this._rawPxBounds.min.subtract(a),this._rawPxBounds.max.add(a)]))},_projectLatlngs:function(i,a,s){var r=i[0]instanceof I,c=i.length,h,_;if(r){for(_=[],h=0;h<c;h++)_[h]=this._map.latLngToLayerPoint(i[h]),s.extend(_[h]);a.push(_)}else for(h=0;h<c;h++)this._projectLatlngs(i[h],a,s)},_clipPoints:function(){var i=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}var a=this._parts,s,r,c,h,_,T,O;for(s=0,c=0,h=this._rings.length;s<h;s++)for(O=this._rings[s],r=0,_=O.length;r<_-1;r++)T=co(O[r],O[r+1],i,r,!0),T&&(a[c]=a[c]||[],a[c].push(T[0]),(T[1]!==O[r+1]||r===_-2)&&(a[c].push(T[1]),c++))}},_simplifyPoints:function(){for(var i=this._parts,a=this.options.smoothFactor,s=0,r=i.length;s<r;s++)i[s]=uo(i[s],a)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(i,a){var s,r,c,h,_,T,O=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(s=0,h=this._parts.length;s<h;s++)for(T=this._parts[s],r=0,_=T.length,c=_-1;r<_;c=r++)if(!(!a&&r===0)&&Wl(i,T[c],T[r])<=O)return!0;return!1}});function Hu(i,a){return new xi(i,a)}xi._flat=fo;var Hn=xi.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return el(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(i){var a=xi.prototype._convertLatLngs.call(this,i),s=a.length;return s>=2&&a[0]instanceof I&&a[0].equals(a[s-1])&&a.pop(),a},_setLatLngs:function(i){xi.prototype._setLatLngs.call(this,i),se(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return se(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var i=this._renderer._bounds,a=this.options.weight,s=new P(a,a);if(i=new R(i.min.subtract(s),i.max.add(s)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}for(var r=0,c=this._rings.length,h;r<c;r++)h=oo(this._rings[r],i,!0),h.length&&this._parts.push(h)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(i){var a=!1,s,r,c,h,_,T,O,H;if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(h=0,O=this._parts.length;h<O;h++)for(s=this._parts[h],_=0,H=s.length,T=H-1;_<H;T=_++)r=s[_],c=s[T],r.y>i.y!=c.y>i.y&&i.x<(c.x-r.x)*(i.y-r.y)/(c.y-r.y)+r.x&&(a=!a);return a||xi.prototype._containsPoint.call(this,i,!0)}});function Ce(i,a){return new Hn(i,a)}var De=Ne.extend({initialize:function(i,a){mt(this,a),this._layers={},i&&this.addData(i)},addData:function(i){var a=Mt(i)?i:i.features,s,r,c;if(a){for(s=0,r=a.length;s<r;s++)c=a[s],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var h=this.options;if(h.filter&&!h.filter(i))return this;var _=za(i,h);return _?(_.feature=qn(i),_.defaultOptions=_.options,this.resetStyle(_),h.onEachFeature&&h.onEachFeature(i,_),this.addLayer(_)):this},resetStyle:function(i){return i===void 0?this.eachLayer(this.resetStyle,this):(i.options=N({},i.defaultOptions),this._setLayerStyle(i,this.options.style),this)},setStyle:function(i){return this.eachLayer(function(a){this._setLayerStyle(a,i)},this)},_setLayerStyle:function(i,a){i.setStyle&&(typeof a=="function"&&(a=a(i.feature)),i.setStyle(a))}});function za(i,a){var s=i.type==="Feature"?i.geometry:i,r=s?s.coordinates:null,c=[],h=a&&a.pointToLayer,_=a&&a.coordsToLatLng||sl,T,O,H,K;if(!r&&!s)return null;switch(s.type){case"Point":return T=_(r),is(h,i,T,a);case"MultiPoint":for(H=0,K=r.length;H<K;H++)T=_(r[H]),c.push(is(h,i,T,a));return new Ne(c);case"LineString":case"MultiLineString":return O=La(r,s.type==="LineString"?0:1,_),new xi(O,a);case"Polygon":case"MultiPolygon":return O=La(r,s.type==="Polygon"?1:2,_),new Hn(O,a);case"GeometryCollection":for(H=0,K=s.geometries.length;H<K;H++){var W=za({geometry:s.geometries[H],type:"Feature",properties:i.properties},a);W&&c.push(W)}return new Ne(c);case"FeatureCollection":for(H=0,K=s.features.length;H<K;H++){var $=za(s.features[H],a);$&&c.push($)}return new Ne(c);default:throw new Error("Invalid GeoJSON object.")}}function is(i,a,s,r){return i?i(a,s):new jn(s,r&&r.markersInheritOptions&&r)}function sl(i){return new I(i[1],i[0],i[2])}function La(i,a,s){for(var r=[],c=0,h=i.length,_;c<h;c++)_=a?La(i[c],a-1,s):(s||sl)(i[c]),r.push(_);return r}function Aa(i,a){return i=X(i),i.alt!==void 0?[ct(i.lng,a),ct(i.lat,a),ct(i.alt,a)]:[ct(i.lng,a),ct(i.lat,a)]}function ol(i,a,s,r){for(var c=[],h=0,_=i.length;h<_;h++)c.push(a?ol(i[h],se(i[h])?0:a-1,s,r):Aa(i[h],r));return!a&&s&&c.length>0&&c.push(c[0].slice()),c}function Ke(i,a){return i.feature?N({},i.feature,{geometry:a}):qn(a)}function qn(i){return i.type==="Feature"||i.type==="FeatureCollection"?i:{type:"Feature",properties:{},geometry:i}}var an={toGeoJSON:function(i){return Ke(this,{type:"Point",coordinates:Aa(this.getLatLng(),i)})}};jn.include(an),es.include(an),Ea.include(an),xi.include({toGeoJSON:function(i){var a=!se(this._latlngs),s=ol(this._latlngs,a?1:0,!1,i);return Ke(this,{type:(a?"Multi":"")+"LineString",coordinates:s})}}),Hn.include({toGeoJSON:function(i){var a=!se(this._latlngs),s=a&&!se(this._latlngs[0]),r=ol(this._latlngs,s?2:a?1:0,!0,i);return a||(r=[r]),Ke(this,{type:(s?"Multi":"")+"Polygon",coordinates:r})}}),nn.include({toMultiPoint:function(i){var a=[];return this.eachLayer(function(s){a.push(s.toGeoJSON(i).geometry.coordinates)}),Ke(this,{type:"MultiPoint",coordinates:a})},toGeoJSON:function(i){var a=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(a==="MultiPoint")return this.toMultiPoint(i);var s=a==="GeometryCollection",r=[];return this.eachLayer(function(c){if(c.toGeoJSON){var h=c.toGeoJSON(i);if(s)r.push(h.geometry);else{var _=qn(h);_.type==="FeatureCollection"?r.push.apply(r,_.features):r.push(_)}}}),s?Ke(this,{geometries:r,type:"GeometryCollection"}):{type:"FeatureCollection",features:r}}});function ul(i,a){return new De(i,a)}var go=ul,ui=Qe.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(i,a,s){this._url=i,this._bounds=J(a),mt(this,s)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(rt(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Dt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(i){return this.options.opacity=i,this._image&&this._updateOpacity(),this},setStyle:function(i){return i.opacity&&this.setOpacity(i.opacity),this},bringToFront:function(){return this._map&&Ji(this._image),this},bringToBack:function(){return this._map&&En(this._image),this},setUrl:function(i){return this._url=i,this._image&&(this._image.src=i),this},setBounds:function(i){return this._bounds=J(i),this._map&&this._reset(),this},getEvents:function(){var i={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var i=this._url.tagName==="IMG",a=this._image=i?this._url:yt("img");if(rt(a,"leaflet-image-layer"),this._zoomAnimated&&rt(a,"leaflet-zoom-animated"),this.options.className&&rt(a,this.options.className),a.onselectstart=tt,a.onmousemove=tt,a.onload=G(this.fire,this,"load"),a.onerror=G(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(a.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),i){this._url=a.src;return}a.src=this._url,a.alt=this.options.alt},_animateZoom:function(i){var a=this._map.getZoomScale(i.zoom),s=this._map._latLngBoundsToNewLayerBounds(this._bounds,i.zoom,i.center).min;qe(this._image,s,a)},_reset:function(){var i=this._image,a=new R(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),s=a.getSize();Gt(i,a.min),i.style.width=s.x+"px",i.style.height=s.y+"px"},_updateOpacity:function(){Me(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var i=this.options.errorOverlayUrl;i&&this._url!==i&&(this._url=i,this._image.src=i)},getCenter:function(){return this._bounds.getCenter()}}),Pn=function(i,a,s){return new ui(i,a,s)},rl=ui.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var i=this._url.tagName==="VIDEO",a=this._image=i?this._url:yt("video");if(rt(a,"leaflet-image-layer"),this._zoomAnimated&&rt(a,"leaflet-zoom-animated"),this.options.className&&rt(a,this.options.className),a.onselectstart=tt,a.onmousemove=tt,a.onloadeddata=G(this.fire,this,"load"),i){for(var s=a.getElementsByTagName("source"),r=[],c=0;c<s.length;c++)r.push(s[c].src);this._url=s.length>0?r:[a.src];return}Mt(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(a.style,"objectFit")&&(a.style.objectFit="fill"),a.autoplay=!!this.options.autoplay,a.loop=!!this.options.loop,a.muted=!!this.options.muted,a.playsInline=!!this.options.playsInline;for(var h=0;h<this._url.length;h++){var _=yt("source");_.src=this._url[h],a.appendChild(_)}}});function yo(i,a,s){return new rl(i,a,s)}var Ni=ui.extend({_initImage:function(){var i=this._image=this._url;rt(i,"leaflet-image-layer"),this._zoomAnimated&&rt(i,"leaflet-zoom-animated"),this.options.className&&rt(i,this.options.className),i.onselectstart=tt,i.onmousemove=tt}});function xo(i,a,s){return new Ni(i,a,s)}var Je=Qe.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(i,a){i&&(i instanceof I||Mt(i))?(this._latlng=X(i),mt(this,a)):(mt(this,i),this._source=a),this.options.content&&(this._content=this.options.content)},openOn:function(i){return i=arguments.length?i:this._source._map,i.hasLayer(this)||i.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(i){return this._map?this.close():(arguments.length?this._source=i:i=this._source,this._prepareOpen(),this.openOn(i._map)),this},onAdd:function(i){this._zoomAnimated=i._zoomAnimated,this._container||this._initLayout(),i._fadeAnimated&&Me(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),i._fadeAnimated&&Me(this._container,1),this.bringToFront(),this.options.interactive&&(rt(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(i){i._fadeAnimated?(Me(this._container,0),this._removeTimeout=setTimeout(G(Dt,void 0,this._container),200)):Dt(this._container),this.options.interactive&&(Rt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(i){return this._latlng=X(i),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(i){return this._content=i,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var i={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ji(this._container),this},bringToBack:function(){return this._map&&En(this._container),this},_prepareOpen:function(i){var a=this._source;if(!a._map)return!1;if(a instanceof Ne){a=null;var s=this._source._layers;for(var r in s)if(s[r]._map){a=s[r];break}if(!a)return!1;this._source=a}if(!i)if(a.getCenter)i=a.getCenter();else if(a.getLatLng)i=a.getLatLng();else if(a.getBounds)i=a.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(i),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var i=this._contentNode,a=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof a=="string")i.innerHTML=a;else{for(;i.hasChildNodes();)i.removeChild(i.firstChild);i.appendChild(a)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var i=this._map.latLngToLayerPoint(this._latlng),a=m(this.options.offset),s=this._getAnchor();this._zoomAnimated?Gt(this._container,i.add(s)):a=a.add(i).add(s);var r=this._containerBottom=-a.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+a.x;this._container.style.bottom=r+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});_t.include({_initOverlay:function(i,a,s,r){var c=a;return c instanceof i||(c=new i(r).setContent(a)),s&&c.setLatLng(s),c}}),Qe.include({_initOverlay:function(i,a,s,r){var c=s;return c instanceof i?(mt(c,r),c._source=this):(c=a&&!r?a:new i(r,this),c.setContent(s)),c}});var Oa=Je.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(i){return i=arguments.length?i:this._source._map,!i.hasLayer(this)&&i._popup&&i._popup.options.autoClose&&i.removeLayer(i._popup),i._popup=this,Je.prototype.openOn.call(this,i)},onAdd:function(i){Je.prototype.onAdd.call(this,i),i.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof yi||this._source.on("preclick",gi))},onRemove:function(i){Je.prototype.onRemove.call(this,i),i.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof yi||this._source.off("preclick",gi))},getEvents:function(){var i=Je.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(i.preclick=this.close),this.options.keepInView&&(i.moveend=this._adjustPan),i},_initLayout:function(){var i="leaflet-popup",a=this._container=yt("div",i+" "+(this.options.className||"")+" leaflet-zoom-animated"),s=this._wrapper=yt("div",i+"-content-wrapper",a);if(this._contentNode=yt("div",i+"-content",s),Dn(a),Cn(this._contentNode),ut(a,"contextmenu",gi),this._tipContainer=yt("div",i+"-tip-container",a),this._tip=yt("div",i+"-tip",this._tipContainer),this.options.closeButton){var r=this._closeButton=yt("a",i+"-close-button",a);r.setAttribute("role","button"),r.setAttribute("aria-label","Close popup"),r.href="#close",r.innerHTML='<span aria-hidden="true">&#215;</span>',ut(r,"click",function(c){Qt(c),this.close()},this)}},_updateLayout:function(){var i=this._contentNode,a=i.style;a.width="",a.whiteSpace="nowrap";var s=i.offsetWidth;s=Math.min(s,this.options.maxWidth),s=Math.max(s,this.options.minWidth),a.width=s+1+"px",a.whiteSpace="",a.height="";var r=i.offsetHeight,c=this.options.maxHeight,h="leaflet-popup-scrolled";c&&r>c?(a.height=c+"px",rt(i,h)):Rt(i,h),this._containerWidth=this._container.offsetWidth},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center),s=this._getAnchor();Gt(this._container,a.add(s))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var i=this._map,a=parseInt(Ki(this._container,"marginBottom"),10)||0,s=this._container.offsetHeight+a,r=this._containerWidth,c=new P(this._containerLeft,-s-this._containerBottom);c._add(Li(this._container));var h=i.layerPointToContainerPoint(c),_=m(this.options.autoPanPadding),T=m(this.options.autoPanPaddingTopLeft||_),O=m(this.options.autoPanPaddingBottomRight||_),H=i.getSize(),K=0,W=0;h.x+r+O.x>H.x&&(K=h.x+r-H.x+O.x),h.x-K-T.x<0&&(K=h.x-T.x),h.y+s+O.y>H.y&&(W=h.y+s-H.y+O.y),h.y-W-T.y<0&&(W=h.y-T.y),(K||W)&&(this.options.keepInView&&(this._autopanning=!0),i.fire("autopanstart").panBy([K,W]))}},_getAnchor:function(){return m(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),qu=function(i,a){return new Oa(i,a)};_t.mergeOptions({closePopupOnClick:!0}),_t.include({openPopup:function(i,a,s){return this._initOverlay(Oa,i,a,s).openOn(this),this},closePopup:function(i){return i=arguments.length?i:this._popup,i&&i.close(),this}}),Qe.include({bindPopup:function(i,a){return this._popup=this._initOverlay(Oa,this._popup,i,a),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(i){return this._popup&&(this instanceof Ne||(this._popup._source=this),this._popup._prepareOpen(i||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(i){return this._popup&&this._popup.setContent(i),this},getPopup:function(){return this._popup},_openPopup:function(i){if(!(!this._popup||!this._map)){oi(i);var a=i.layer||i.target;if(this._popup._source===a&&!(a instanceof yi)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(i.latlng);return}this._popup._source=a,this.openPopup(i.latlng)}},_movePopup:function(i){this._popup.setLatLng(i.latlng)},_onKeyPress:function(i){i.originalEvent.keyCode===13&&this._openPopup(i)}});var cl=Je.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(i){Je.prototype.onAdd.call(this,i),this.setOpacity(this.options.opacity),i.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(i){Je.prototype.onRemove.call(this,i),i.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var i=Je.prototype.getEvents.call(this);return this.options.permanent||(i.preclick=this.close),i},_initLayout:function(){var i="leaflet-tooltip",a=i+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=yt("div",a),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+C(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(i){var a,s,r=this._map,c=this._container,h=r.latLngToContainerPoint(r.getCenter()),_=r.layerPointToContainerPoint(i),T=this.options.direction,O=c.offsetWidth,H=c.offsetHeight,K=m(this.options.offset),W=this._getAnchor();T==="top"?(a=O/2,s=H):T==="bottom"?(a=O/2,s=0):T==="center"?(a=O/2,s=H/2):T==="right"?(a=0,s=H/2):T==="left"?(a=O,s=H/2):_.x<h.x?(T="right",a=0,s=H/2):(T="left",a=O+(K.x+W.x)*2,s=H/2),i=i.subtract(m(a,s,!0)).add(K).add(W),Rt(c,"leaflet-tooltip-right"),Rt(c,"leaflet-tooltip-left"),Rt(c,"leaflet-tooltip-top"),Rt(c,"leaflet-tooltip-bottom"),rt(c,"leaflet-tooltip-"+T),Gt(c,i)},_updatePosition:function(){var i=this._map.latLngToLayerPoint(this._latlng);this._setPosition(i)},setOpacity:function(i){this.options.opacity=i,this._container&&Me(this._container,i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center);this._setPosition(a)},_getAnchor:function(){return m(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Pu=function(i,a){return new cl(i,a)};_t.include({openTooltip:function(i,a,s){return this._initOverlay(cl,i,a,s).openOn(this),this},closeTooltip:function(i){return i.close(),this}}),Qe.include({bindTooltip:function(i,a){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(cl,this._tooltip,i,a),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(i){if(!(!i&&this._tooltipHandlersAdded)){var a=i?"off":"on",s={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?s.add=this._openTooltip:(s.mouseover=this._openTooltip,s.mouseout=this.closeTooltip,s.click=this._openTooltip,this._map?this._addFocusListeners():s.add=this._addFocusListeners),this._tooltip.options.sticky&&(s.mousemove=this._moveTooltip),this[a](s),this._tooltipHandlersAdded=!i}},openTooltip:function(i){return this._tooltip&&(this instanceof Ne||(this._tooltip._source=this),this._tooltip._prepareOpen(i)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(i){return this._tooltip&&this._tooltip.setContent(i),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&(ut(a,"focus",function(){this._tooltip._source=i,this.openTooltip()},this),ut(a,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&a.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(i){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var a=this;this._map.once("moveend",function(){a._openOnceFlag=!1,a._openTooltip(i)});return}this._tooltip._source=i.layer||i.target,this.openTooltip(this._tooltip.options.sticky?i.latlng:void 0)}},_moveTooltip:function(i){var a=i.latlng,s,r;this._tooltip.options.sticky&&i.originalEvent&&(s=this._map.mouseEventToContainerPoint(i.originalEvent),r=this._map.containerPointToLayerPoint(s),a=this._map.layerPointToLatLng(r)),this._tooltip.setLatLng(a)}});var ns=Zn.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(i){var a=i&&i.tagName==="DIV"?i:document.createElement("div"),s=this.options;if(s.html instanceof Element?(me(a),a.appendChild(s.html)):a.innerHTML=s.html!==!1?s.html:"",s.bgPos){var r=m(s.bgPos);a.style.backgroundPosition=-r.x+"px "+-r.y+"px"}return this._setIconStyles(a,"icon"),a},createShadow:function(){return null}});function bo(i){return new ns(i)}Zn.Default=Un;var ln=Qe.extend({options:{tileSize:256,opacity:1,updateWhenIdle:et.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(i){mt(this,i)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(i){i._addZoomLimit(this)},onRemove:function(i){this._removeAllTiles(),Dt(this._container),i._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ji(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(En(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(i){return this.options.opacity=i,this._updateOpacity(),this},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var i=this._clampZoom(this._map.getZoom());i!==this._tileZoom&&(this._tileZoom=i,this._updateLevels()),this._update()}return this},getEvents:function(){var i={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=B(this._onMoveEnd,this.options.updateInterval,this)),i.move=this._onMove),this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},createTile:function(){return document.createElement("div")},getTileSize:function(){var i=this.options.tileSize;return i instanceof P?i:new P(i,i)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(i){for(var a=this.getPane().children,s=-i(-1/0,1/0),r=0,c=a.length,h;r<c;r++)h=a[r].style.zIndex,a[r]!==this._container&&h&&(s=i(s,+h));isFinite(s)&&(this.options.zIndex=s+i(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!et.ielt9){Me(this._container,this.options.opacity);var i=+new Date,a=!1,s=!1;for(var r in this._tiles){var c=this._tiles[r];if(!(!c.current||!c.loaded)){var h=Math.min(1,(i-c.loaded)/200);Me(c.el,h),h<1?a=!0:(c.active?s=!0:this._onOpaqueTile(c),c.active=!0)}}s&&!this._noPrune&&this._pruneTiles(),a&&(Et(this._fadeFrame),this._fadeFrame=jt(this._updateOpacity,this))}},_onOpaqueTile:tt,_initContainer:function(){this._container||(this._container=yt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var i=this._tileZoom,a=this.options.maxZoom;if(i!==void 0){for(var s in this._levels)s=Number(s),this._levels[s].el.children.length||s===i?(this._levels[s].el.style.zIndex=a-Math.abs(i-s),this._onUpdateLevel(s)):(Dt(this._levels[s].el),this._removeTilesAtZoom(s),this._onRemoveLevel(s),delete this._levels[s]);var r=this._levels[i],c=this._map;return r||(r=this._levels[i]={},r.el=yt("div","leaflet-tile-container leaflet-zoom-animated",this._container),r.el.style.zIndex=a,r.origin=c.project(c.unproject(c.getPixelOrigin()),i).round(),r.zoom=i,this._setZoomTransform(r,c.getCenter(),c.getZoom()),tt(r.el.offsetWidth),this._onCreateLevel(r)),this._level=r,r}},_onUpdateLevel:tt,_onRemoveLevel:tt,_onCreateLevel:tt,_pruneTiles:function(){if(this._map){var i,a,s=this._map.getZoom();if(s>this.options.maxZoom||s<this.options.minZoom){this._removeAllTiles();return}for(i in this._tiles)a=this._tiles[i],a.retain=a.current;for(i in this._tiles)if(a=this._tiles[i],a.current&&!a.active){var r=a.coords;this._retainParent(r.x,r.y,r.z,r.z-5)||this._retainChildren(r.x,r.y,r.z,r.z+2)}for(i in this._tiles)this._tiles[i].retain||this._removeTile(i)}},_removeTilesAtZoom:function(i){for(var a in this._tiles)this._tiles[a].coords.z===i&&this._removeTile(a)},_removeAllTiles:function(){for(var i in this._tiles)this._removeTile(i)},_invalidateAll:function(){for(var i in this._levels)Dt(this._levels[i].el),this._onRemoveLevel(Number(i)),delete this._levels[i];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(i,a,s,r){var c=Math.floor(i/2),h=Math.floor(a/2),_=s-1,T=new P(+c,+h);T.z=+_;var O=this._tileCoordsToKey(T),H=this._tiles[O];return H&&H.active?(H.retain=!0,!0):(H&&H.loaded&&(H.retain=!0),_>r?this._retainParent(c,h,_,r):!1)},_retainChildren:function(i,a,s,r){for(var c=2*i;c<2*i+2;c++)for(var h=2*a;h<2*a+2;h++){var _=new P(c,h);_.z=s+1;var T=this._tileCoordsToKey(_),O=this._tiles[T];if(O&&O.active){O.retain=!0;continue}else O&&O.loaded&&(O.retain=!0);s+1<r&&this._retainChildren(c,h,s+1,r)}},_resetView:function(i){var a=i&&(i.pinch||i.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),a,a)},_animateZoom:function(i){this._setView(i.center,i.zoom,!0,i.noUpdate)},_clampZoom:function(i){var a=this.options;return a.minNativeZoom!==void 0&&i<a.minNativeZoom?a.minNativeZoom:a.maxNativeZoom!==void 0&&a.maxNativeZoom<i?a.maxNativeZoom:i},_setView:function(i,a,s,r){var c=Math.round(a);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var h=this.options.updateWhenZooming&&c!==this._tileZoom;(!r||h)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(i),s||this._pruneTiles(),this._noPrune=!!s),this._setZoomTransforms(i,a)},_setZoomTransforms:function(i,a){for(var s in this._levels)this._setZoomTransform(this._levels[s],i,a)},_setZoomTransform:function(i,a,s){var r=this._map.getZoomScale(s,i.zoom),c=i.origin.multiplyBy(r).subtract(this._map._getNewPixelOrigin(a,s)).round();et.any3d?qe(i.el,c,r):Gt(i.el,c)},_resetGrid:function(){var i=this._map,a=i.options.crs,s=this._tileSize=this.getTileSize(),r=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=a.wrapLng&&!this.options.noWrap&&[Math.floor(i.project([0,a.wrapLng[0]],r).x/s.x),Math.ceil(i.project([0,a.wrapLng[1]],r).x/s.y)],this._wrapY=a.wrapLat&&!this.options.noWrap&&[Math.floor(i.project([a.wrapLat[0],0],r).y/s.x),Math.ceil(i.project([a.wrapLat[1],0],r).y/s.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(i){var a=this._map,s=a._animatingZoom?Math.max(a._animateToZoom,a.getZoom()):a.getZoom(),r=a.getZoomScale(s,this._tileZoom),c=a.project(i,this._tileZoom).floor(),h=a.getSize().divideBy(r*2);return new R(c.subtract(h),c.add(h))},_update:function(i){var a=this._map;if(a){var s=this._clampZoom(a.getZoom());if(i===void 0&&(i=a.getCenter()),this._tileZoom!==void 0){var r=this._getTiledPixelBounds(i),c=this._pxBoundsToTileRange(r),h=c.getCenter(),_=[],T=this.options.keepBuffer,O=new R(c.getBottomLeft().subtract([T,-T]),c.getTopRight().add([T,-T]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var H in this._tiles){var K=this._tiles[H].coords;(K.z!==this._tileZoom||!O.contains(new P(K.x,K.y)))&&(this._tiles[H].current=!1)}if(Math.abs(s-this._tileZoom)>1){this._setView(i,s);return}for(var W=c.min.y;W<=c.max.y;W++)for(var $=c.min.x;$<=c.max.x;$++){var ot=new P($,W);if(ot.z=this._tileZoom,!!this._isValidTile(ot)){var Bt=this._tiles[this._tileCoordsToKey(ot)];Bt?Bt.current=!0:_.push(ot)}}if(_.sort(function(oe,Pe){return oe.distanceTo(h)-Pe.distanceTo(h)}),_.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var Yt=document.createDocumentFragment();for($=0;$<_.length;$++)this._addTile(_[$],Yt);this._level.el.appendChild(Yt)}}}},_isValidTile:function(i){var a=this._map.options.crs;if(!a.infinite){var s=this._globalTileRange;if(!a.wrapLng&&(i.x<s.min.x||i.x>s.max.x)||!a.wrapLat&&(i.y<s.min.y||i.y>s.max.y))return!1}if(!this.options.bounds)return!0;var r=this._tileCoordsToBounds(i);return J(this.options.bounds).overlaps(r)},_keyToBounds:function(i){return this._tileCoordsToBounds(this._keyToTileCoords(i))},_tileCoordsToNwSe:function(i){var a=this._map,s=this.getTileSize(),r=i.scaleBy(s),c=r.add(s),h=a.unproject(r,i.z),_=a.unproject(c,i.z);return[h,_]},_tileCoordsToBounds:function(i){var a=this._tileCoordsToNwSe(i),s=new k(a[0],a[1]);return this.options.noWrap||(s=this._map.wrapLatLngBounds(s)),s},_tileCoordsToKey:function(i){return i.x+":"+i.y+":"+i.z},_keyToTileCoords:function(i){var a=i.split(":"),s=new P(+a[0],+a[1]);return s.z=+a[2],s},_removeTile:function(i){var a=this._tiles[i];a&&(Dt(a.el),delete this._tiles[i],this.fire("tileunload",{tile:a.el,coords:this._keyToTileCoords(i)}))},_initTile:function(i){rt(i,"leaflet-tile");var a=this.getTileSize();i.style.width=a.x+"px",i.style.height=a.y+"px",i.onselectstart=tt,i.onmousemove=tt,et.ielt9&&this.options.opacity<1&&Me(i,this.options.opacity)},_addTile:function(i,a){var s=this._getTilePos(i),r=this._tileCoordsToKey(i),c=this.createTile(this._wrapCoords(i),G(this._tileReady,this,i));this._initTile(c),this.createTile.length<2&&jt(G(this._tileReady,this,i,null,c)),Gt(c,s),this._tiles[r]={el:c,coords:i,current:!0},a.appendChild(c),this.fire("tileloadstart",{tile:c,coords:i})},_tileReady:function(i,a,s){a&&this.fire("tileerror",{error:a,tile:s,coords:i});var r=this._tileCoordsToKey(i);s=this._tiles[r],s&&(s.loaded=+new Date,this._map._fadeAnimated?(Me(s.el,0),Et(this._fadeFrame),this._fadeFrame=jt(this._updateOpacity,this)):(s.active=!0,this._pruneTiles()),a||(rt(s.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:s.el,coords:i})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),et.ielt9||!this._map._fadeAnimated?jt(this._pruneTiles,this):setTimeout(G(this._pruneTiles,this),250)))},_getTilePos:function(i){return i.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(i){var a=new P(this._wrapX?q(i.x,this._wrapX):i.x,this._wrapY?q(i.y,this._wrapY):i.y);return a.z=i.z,a},_pxBoundsToTileRange:function(i){var a=this.getTileSize();return new R(i.min.unscaleBy(a).floor(),i.max.unscaleBy(a).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var i in this._tiles)if(!this._tiles[i].loaded)return!1;return!0}});function We(i){return new ln(i)}var sn=ln.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(i,a){this._url=i,a=mt(this,a),a.detectRetina&&et.retina&&a.maxZoom>0?(a.tileSize=Math.floor(a.tileSize/2),a.zoomReverse?(a.zoomOffset--,a.minZoom=Math.min(a.maxZoom,a.minZoom+1)):(a.zoomOffset++,a.maxZoom=Math.max(a.minZoom,a.maxZoom-1)),a.minZoom=Math.max(0,a.minZoom)):a.zoomReverse?a.minZoom=Math.min(a.maxZoom,a.minZoom):a.maxZoom=Math.max(a.minZoom,a.maxZoom),typeof a.subdomains=="string"&&(a.subdomains=a.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(i,a){return this._url===i&&a===void 0&&(a=!0),this._url=i,a||this.redraw(),this},createTile:function(i,a){var s=document.createElement("img");return ut(s,"load",G(this._tileOnLoad,this,a,s)),ut(s,"error",G(this._tileOnError,this,a,s)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(s.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(s.referrerPolicy=this.options.referrerPolicy),s.alt="",s.src=this.getTileUrl(i),s},getTileUrl:function(i){var a={r:et.retina?"@2x":"",s:this._getSubdomain(i),x:i.x,y:i.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var s=this._globalTileRange.max.y-i.y;this.options.tms&&(a.y=s),a["-y"]=s}return Ae(this._url,N(a,this.options))},_tileOnLoad:function(i,a){et.ielt9?setTimeout(G(i,this,null,a),0):i(null,a)},_tileOnError:function(i,a,s){var r=this.options.errorTileUrl;r&&a.getAttribute("src")!==r&&(a.src=r),i(s,a)},_onTileRemove:function(i){i.tile.onload=null},_getZoomForUrl:function(){var i=this._tileZoom,a=this.options.maxZoom,s=this.options.zoomReverse,r=this.options.zoomOffset;return s&&(i=a-i),i+r},_getSubdomain:function(i){var a=Math.abs(i.x+i.y)%this.options.subdomains.length;return this.options.subdomains[a]},_abortLoading:function(){var i,a;for(i in this._tiles)if(this._tiles[i].coords.z!==this._tileZoom&&(a=this._tiles[i].el,a.onload=tt,a.onerror=tt,!a.complete)){a.src=ht;var s=this._tiles[i].coords;Dt(a),delete this._tiles[i],this.fire("tileabort",{tile:a,coords:s})}},_removeTile:function(i){var a=this._tiles[i];if(a)return a.el.setAttribute("src",ht),ln.prototype._removeTile.call(this,i)},_tileReady:function(i,a,s){if(!(!this._map||s&&s.getAttribute("src")===ht))return ln.prototype._tileReady.call(this,i,a,s)}});function Re(i,a){return new sn(i,a)}var Be=sn.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(i,a){this._url=i;var s=N({},this.defaultWmsParams);for(var r in a)r in this.options||(s[r]=a[r]);a=mt(this,a);var c=a.detectRetina&&et.retina?2:1,h=this.getTileSize();s.width=h.x*c,s.height=h.y*c,this.wmsParams=s},onAdd:function(i){this._crs=this.options.crs||i.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var a=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[a]=this._crs.code,sn.prototype.onAdd.call(this,i)},getTileUrl:function(i){var a=this._tileCoordsToNwSe(i),s=this._crs,r=V(s.project(a[0]),s.project(a[1])),c=r.min,h=r.max,_=(this._wmsVersion>=1.3&&this._crs===_o?[c.y,c.x,h.y,h.x]:[c.x,c.y,h.x,h.y]).join(","),T=sn.prototype.getTileUrl.call(this,i);return T+Xt(this.wmsParams,T,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+_},setParams:function(i,a){return N(this.wmsParams,i),a||this.redraw(),this}});function kn(i,a){return new Be(i,a)}sn.WMS=Be,Re.wms=kn;var Fe=Qe.extend({options:{padding:.1},initialize:function(i){mt(this,i),C(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),rt(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var i={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(i.zoomanim=this._onAnimZoom),i},_onAnimZoom:function(i){this._updateTransform(i.center,i.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(i,a){var s=this._map.getZoomScale(a,this._zoom),r=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,a),h=r.multiplyBy(-s).add(c).subtract(this._map._getNewPixelOrigin(i,a));et.any3d?qe(this._container,h,s):Gt(this._container,h)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var i in this._layers)this._layers[i]._reset()},_onZoomEnd:function(){for(var i in this._layers)this._layers[i]._project()},_updatePaths:function(){for(var i in this._layers)this._layers[i]._update()},_update:function(){var i=this.options.padding,a=this._map.getSize(),s=this._map.containerPointToLayerPoint(a.multiplyBy(-i)).round();this._bounds=new R(s,s.add(a.multiplyBy(1+i*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Na=Fe.extend({options:{tolerance:0},getEvents:function(){var i=Fe.prototype.getEvents.call(this);return i.viewprereset=this._onViewPreReset,i},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){Fe.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var i=this._container=document.createElement("canvas");ut(i,"mousemove",this._onMouseMove,this),ut(i,"click dblclick mousedown mouseup contextmenu",this._onClick,this),ut(i,"mouseout",this._handleMouseOut,this),i._leaflet_disable_events=!0,this._ctx=i.getContext("2d")},_destroyContainer:function(){Et(this._redrawRequest),delete this._ctx,Dt(this._container),St(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var i;this._redrawBounds=null;for(var a in this._layers)i=this._layers[a],i._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=this._container,s=i.getSize(),r=et.retina?2:1;Gt(a,i.min),a.width=r*s.x,a.height=r*s.y,a.style.width=s.x+"px",a.style.height=s.y+"px",et.retina&&this._ctx.scale(2,2),this._ctx.translate(-i.min.x,-i.min.y),this.fire("update")}},_reset:function(){Fe.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(i){this._updateDashArray(i),this._layers[C(i)]=i;var a=i._order={layer:i,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=a),this._drawLast=a,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(i){this._requestRedraw(i)},_removePath:function(i){var a=i._order,s=a.next,r=a.prev;s?s.prev=r:this._drawLast=r,r?r.next=s:this._drawFirst=s,delete i._order,delete this._layers[C(i)],this._requestRedraw(i)},_updatePath:function(i){this._extendRedrawBounds(i),i._project(),i._update(),this._requestRedraw(i)},_updateStyle:function(i){this._updateDashArray(i),this._requestRedraw(i)},_updateDashArray:function(i){if(typeof i.options.dashArray=="string"){var a=i.options.dashArray.split(/[, ]+/),s=[],r,c;for(c=0;c<a.length;c++){if(r=Number(a[c]),isNaN(r))return;s.push(r)}i.options._dashArray=s}else i.options._dashArray=i.options.dashArray},_requestRedraw:function(i){this._map&&(this._extendRedrawBounds(i),this._redrawRequest=this._redrawRequest||jt(this._redraw,this))},_extendRedrawBounds:function(i){if(i._pxBounds){var a=(i.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new R,this._redrawBounds.extend(i._pxBounds.min.subtract([a,a])),this._redrawBounds.extend(i._pxBounds.max.add([a,a]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var i=this._redrawBounds;if(i){var a=i.getSize();this._ctx.clearRect(i.min.x,i.min.y,a.x,a.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var i,a=this._redrawBounds;if(this._ctx.save(),a){var s=a.getSize();this._ctx.beginPath(),this._ctx.rect(a.min.x,a.min.y,s.x,s.y),this._ctx.clip()}this._drawing=!0;for(var r=this._drawFirst;r;r=r.next)i=r.layer,(!a||i._pxBounds&&i._pxBounds.intersects(a))&&i._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(i,a){if(this._drawing){var s,r,c,h,_=i._parts,T=_.length,O=this._ctx;if(T){for(O.beginPath(),s=0;s<T;s++){for(r=0,c=_[s].length;r<c;r++)h=_[s][r],O[r?"lineTo":"moveTo"](h.x,h.y);a&&O.closePath()}this._fillStroke(O,i)}}},_updateCircle:function(i){if(!(!this._drawing||i._empty())){var a=i._point,s=this._ctx,r=Math.max(Math.round(i._radius),1),c=(Math.max(Math.round(i._radiusY),1)||r)/r;c!==1&&(s.save(),s.scale(1,c)),s.beginPath(),s.arc(a.x,a.y/c,r,0,Math.PI*2,!1),c!==1&&s.restore(),this._fillStroke(s,i)}},_fillStroke:function(i,a){var s=a.options;s.fill&&(i.globalAlpha=s.fillOpacity,i.fillStyle=s.fillColor||s.color,i.fill(s.fillRule||"evenodd")),s.stroke&&s.weight!==0&&(i.setLineDash&&i.setLineDash(a.options&&a.options._dashArray||[]),i.globalAlpha=s.opacity,i.lineWidth=s.weight,i.strokeStyle=s.color,i.lineCap=s.lineCap,i.lineJoin=s.lineJoin,i.stroke())},_onClick:function(i){for(var a=this._map.mouseEventToLayerPoint(i),s,r,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(!(i.type==="click"||i.type==="preclick")||!this._map._draggableMoved(s))&&(r=s);this._fireEvent(r?[r]:!1,i)},_onMouseMove:function(i){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var a=this._map.mouseEventToLayerPoint(i);this._handleMouseHover(i,a)}},_handleMouseOut:function(i){var a=this._hoveredLayer;a&&(Rt(this._container,"leaflet-interactive"),this._fireEvent([a],i,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(i,a){if(!this._mouseHoverThrottled){for(var s,r,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(r=s);r!==this._hoveredLayer&&(this._handleMouseOut(i),r&&(rt(this._container,"leaflet-interactive"),this._fireEvent([r],i,"mouseover"),this._hoveredLayer=r)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,i),this._mouseHoverThrottled=!0,setTimeout(G(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(i,a,s){this._map._fireDOMEvent(a,s||a.type,i)},_bringToFront:function(i){var a=i._order;if(a){var s=a.next,r=a.prev;if(s)s.prev=r;else return;r?r.next=s:s&&(this._drawFirst=s),a.prev=this._drawLast,this._drawLast.next=a,a.next=null,this._drawLast=a,this._requestRedraw(i)}},_bringToBack:function(i){var a=i._order;if(a){var s=a.next,r=a.prev;if(r)r.next=s;else return;s?s.prev=r:r&&(this._drawLast=r),a.prev=null,a.next=this._drawFirst,this._drawFirst.prev=a,this._drawFirst=a,this._requestRedraw(i)}}});function Ca(i){return et.canvas?new Na(i):null}var on=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(i){return document.createElement("<lvml:"+i+' class="lvml">')}}catch{}return function(i){return document.createElement("<"+i+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Gn={_initContainer:function(){this._container=yt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(Fe.prototype._update.call(this),this.fire("update"))},_initPath:function(i){var a=i._container=on("shape");rt(a,"leaflet-vml-shape "+(this.options.className||"")),a.coordsize="1 1",i._path=on("path"),a.appendChild(i._path),this._updateStyle(i),this._layers[C(i)]=i},_addPath:function(i){var a=i._container;this._container.appendChild(a),i.options.interactive&&i.addInteractiveTarget(a)},_removePath:function(i){var a=i._container;Dt(a),i.removeInteractiveTarget(a),delete this._layers[C(i)]},_updateStyle:function(i){var a=i._stroke,s=i._fill,r=i.options,c=i._container;c.stroked=!!r.stroke,c.filled=!!r.fill,r.stroke?(a||(a=i._stroke=on("stroke")),c.appendChild(a),a.weight=r.weight+"px",a.color=r.color,a.opacity=r.opacity,r.dashArray?a.dashStyle=Mt(r.dashArray)?r.dashArray.join(" "):r.dashArray.replace(/( *, *)/g," "):a.dashStyle="",a.endcap=r.lineCap.replace("butt","flat"),a.joinstyle=r.lineJoin):a&&(c.removeChild(a),i._stroke=null),r.fill?(s||(s=i._fill=on("fill")),c.appendChild(s),s.color=r.fillColor||r.color,s.opacity=r.fillOpacity):s&&(c.removeChild(s),i._fill=null)},_updateCircle:function(i){var a=i._point.round(),s=Math.round(i._radius),r=Math.round(i._radiusY||s);this._setPath(i,i._empty()?"M0 0":"AL "+a.x+","+a.y+" "+s+","+r+" 0,"+65535*360)},_setPath:function(i,a){i._path.v=a},_bringToFront:function(i){Ji(i._container)},_bringToBack:function(i){En(i._container)}},Da=et.vml?on:ks,Ci=Fe.extend({_initContainer:function(){this._container=Da("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Da("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Dt(this._container),St(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=i.getSize(),s=this._container;(!this._svgSize||!this._svgSize.equals(a))&&(this._svgSize=a,s.setAttribute("width",a.x),s.setAttribute("height",a.y)),Gt(s,i.min),s.setAttribute("viewBox",[i.min.x,i.min.y,a.x,a.y].join(" ")),this.fire("update")}},_initPath:function(i){var a=i._path=Da("path");i.options.className&&rt(a,i.options.className),i.options.interactive&&rt(a,"leaflet-interactive"),this._updateStyle(i),this._layers[C(i)]=i},_addPath:function(i){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(i._path),i.addInteractiveTarget(i._path)},_removePath:function(i){Dt(i._path),i.removeInteractiveTarget(i._path),delete this._layers[C(i)]},_updatePath:function(i){i._project(),i._update()},_updateStyle:function(i){var a=i._path,s=i.options;a&&(s.stroke?(a.setAttribute("stroke",s.color),a.setAttribute("stroke-opacity",s.opacity),a.setAttribute("stroke-width",s.weight),a.setAttribute("stroke-linecap",s.lineCap),a.setAttribute("stroke-linejoin",s.lineJoin),s.dashArray?a.setAttribute("stroke-dasharray",s.dashArray):a.removeAttribute("stroke-dasharray"),s.dashOffset?a.setAttribute("stroke-dashoffset",s.dashOffset):a.removeAttribute("stroke-dashoffset")):a.setAttribute("stroke","none"),s.fill?(a.setAttribute("fill",s.fillColor||s.color),a.setAttribute("fill-opacity",s.fillOpacity),a.setAttribute("fill-rule",s.fillRule||"evenodd")):a.setAttribute("fill","none"))},_updatePoly:function(i,a){this._setPath(i,Gs(i._parts,a))},_updateCircle:function(i){var a=i._point,s=Math.max(Math.round(i._radius),1),r=Math.max(Math.round(i._radiusY),1)||s,c="a"+s+","+r+" 0 1,0 ",h=i._empty()?"M0 0":"M"+(a.x-s)+","+a.y+c+s*2+",0 "+c+-s*2+",0 ";this._setPath(i,h)},_setPath:function(i,a){i._path.setAttribute("d",a)},_bringToFront:function(i){Ji(i._path)},_bringToBack:function(i){En(i._path)}});et.vml&&Ci.include(Gn);function un(i){return et.svg||et.vml?new Ci(i):null}_t.include({getRenderer:function(i){var a=i.options.renderer||this._getPaneRenderer(i.options.pane)||this.options.renderer||this._renderer;return a||(a=this._renderer=this._createRenderer()),this.hasLayer(a)||this.addLayer(a),a},_getPaneRenderer:function(i){if(i==="overlayPane"||i===void 0)return!1;var a=this._paneRenderers[i];return a===void 0&&(a=this._createRenderer({pane:i}),this._paneRenderers[i]=a),a},_createRenderer:function(i){return this.options.preferCanvas&&Ca(i)||un(i)}});var So=Hn.extend({initialize:function(i,a){Hn.prototype.initialize.call(this,this._boundsToLatLngs(i),a)},setBounds:function(i){return this.setLatLngs(this._boundsToLatLngs(i))},_boundsToLatLngs:function(i){return i=J(i),[i.getSouthWest(),i.getNorthWest(),i.getNorthEast(),i.getSouthEast()]}});function Ze(i,a){return new So(i,a)}Ci.create=Da,Ci.pointsToPath=Gs,De.geometryToLayer=za,De.coordsToLatLng=sl,De.coordsToLatLngs=La,De.latLngToCoords=Aa,De.latLngsToCoords=ol,De.getFeature=Ke,De.asFeature=qn,_t.mergeOptions({boxZoom:!0});var fl=Xe.extend({initialize:function(i){this._map=i,this._container=i._container,this._pane=i._panes.overlayPane,this._resetStateTimeout=0,i.on("unload",this._destroy,this)},addHooks:function(){ut(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){St(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Dt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(i){if(!i.shiftKey||i.which!==1&&i.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),ai(),xa(),this._startPoint=this._map.mouseEventToContainerPoint(i),ut(document,{contextmenu:oi,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(i){this._moved||(this._moved=!0,this._box=yt("div","leaflet-zoom-box",this._container),rt(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(i);var a=new R(this._point,this._startPoint),s=a.getSize();Gt(this._box,a.min),this._box.style.width=s.x+"px",this._box.style.height=s.y+"px"},_finish:function(){this._moved&&(Dt(this._box),Rt(this._container,"leaflet-crosshair")),ya(),Yl(),St(document,{contextmenu:oi,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(i){if(!(i.which!==1&&i.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(G(this._resetState,this),0);var a=new k(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(a).fire("boxzoomend",{boxZoomBounds:a})}},_onKeyDown:function(i){i.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});_t.addInitHook("addHandler","boxZoom",fl),_t.mergeOptions({doubleClickZoom:!0});var ri=Xe.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(i){var a=this._map,s=a.getZoom(),r=a.options.zoomDelta,c=i.originalEvent.shiftKey?s-r:s+r;a.options.doubleClickZoom==="center"?a.setZoom(c):a.setZoomAround(i.containerPoint,c)}});_t.addInitHook("addHandler","doubleClickZoom",ri),_t.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var as=Xe.extend({addHooks:function(){if(!this._draggable){var i=this._map;this._draggable=new Oi(i._mapPane,i._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),i.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),i.on("zoomend",this._onZoomEnd,this),i.whenReady(this._onZoomEnd,this))}rt(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Rt(this._map._container,"leaflet-grab"),Rt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var i=this._map;if(i._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var a=J(this._map.options.maxBounds);this._offsetLimit=V(this._map.latLngToContainerPoint(a.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(a.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;i.fire("movestart").fire("dragstart"),i.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(i){if(this._map.options.inertia){var a=this._lastTime=+new Date,s=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(s),this._times.push(a),this._prunePositions(a)}this._map.fire("move",i).fire("drag",i)},_prunePositions:function(i){for(;this._positions.length>1&&i-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var i=this._map.getSize().divideBy(2),a=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=a.subtract(i).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(i,a){return i-(i-a)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var i=this._draggable._newPos.subtract(this._draggable._startPos),a=this._offsetLimit;i.x<a.min.x&&(i.x=this._viscousLimit(i.x,a.min.x)),i.y<a.min.y&&(i.y=this._viscousLimit(i.y,a.min.y)),i.x>a.max.x&&(i.x=this._viscousLimit(i.x,a.max.x)),i.y>a.max.y&&(i.y=this._viscousLimit(i.y,a.max.y)),this._draggable._newPos=this._draggable._startPos.add(i)}},_onPreDragWrap:function(){var i=this._worldWidth,a=Math.round(i/2),s=this._initialWorldOffset,r=this._draggable._newPos.x,c=(r-a+s)%i+a-s,h=(r+a+s)%i-a-s,_=Math.abs(c+s)<Math.abs(h+s)?c:h;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=_},_onDragEnd:function(i){var a=this._map,s=a.options,r=!s.inertia||i.noInertia||this._times.length<2;if(a.fire("dragend",i),r)a.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),h=(this._lastTime-this._times[0])/1e3,_=s.easeLinearity,T=c.multiplyBy(_/h),O=T.distanceTo([0,0]),H=Math.min(s.inertiaMaxSpeed,O),K=T.multiplyBy(H/O),W=H/(s.inertiaDeceleration*_),$=K.multiplyBy(-W/2).round();!$.x&&!$.y?a.fire("moveend"):($=a._limitOffset($,a.options.maxBounds),jt(function(){a.panBy($,{duration:W,easeLinearity:_,noMoveStart:!0,animate:!0})}))}}});_t.addInitHook("addHandler","dragging",as),_t.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Ra=Xe.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(i){this._map=i,this._setPanDelta(i.options.keyboardPanDelta),this._setZoomDelta(i.options.zoomDelta)},addHooks:function(){var i=this._map._container;i.tabIndex<=0&&(i.tabIndex="0"),ut(i,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),St(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var i=document.body,a=document.documentElement,s=i.scrollTop||a.scrollTop,r=i.scrollLeft||a.scrollLeft;this._map._container.focus(),window.scrollTo(r,s)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(i){var a=this._panKeys={},s=this.keyCodes,r,c;for(r=0,c=s.left.length;r<c;r++)a[s.left[r]]=[-1*i,0];for(r=0,c=s.right.length;r<c;r++)a[s.right[r]]=[i,0];for(r=0,c=s.down.length;r<c;r++)a[s.down[r]]=[0,i];for(r=0,c=s.up.length;r<c;r++)a[s.up[r]]=[0,-1*i]},_setZoomDelta:function(i){var a=this._zoomKeys={},s=this.keyCodes,r,c;for(r=0,c=s.zoomIn.length;r<c;r++)a[s.zoomIn[r]]=i;for(r=0,c=s.zoomOut.length;r<c;r++)a[s.zoomOut[r]]=-i},_addHooks:function(){ut(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){St(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(i){if(!(i.altKey||i.ctrlKey||i.metaKey)){var a=i.keyCode,s=this._map,r;if(a in this._panKeys){if(!s._panAnim||!s._panAnim._inProgress)if(r=this._panKeys[a],i.shiftKey&&(r=m(r).multiplyBy(3)),s.options.maxBounds&&(r=s._limitOffset(m(r),s.options.maxBounds)),s.options.worldCopyJump){var c=s.wrapLatLng(s.unproject(s.project(s.getCenter()).add(r)));s.panTo(c)}else s.panBy(r)}else if(a in this._zoomKeys)s.setZoom(s.getZoom()+(i.shiftKey?3:1)*this._zoomKeys[a]);else if(a===27&&s._popup&&s._popup.options.closeOnEscapeKey)s.closePopup();else return;oi(i)}}});_t.addInitHook("addHandler","keyboard",Ra),_t.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Di=Xe.extend({addHooks:function(){ut(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){St(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(i){var a=Ta(i),s=this._map.options.wheelDebounceTime;this._delta+=a,this._lastMousePos=this._map.mouseEventToContainerPoint(i),this._startTime||(this._startTime=+new Date);var r=Math.max(s-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(G(this._performZoom,this),r),oi(i)},_performZoom:function(){var i=this._map,a=i.getZoom(),s=this._map.options.zoomSnap||0;i._stop();var r=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(r))))/Math.LN2,h=s?Math.ceil(c/s)*s:c,_=i._limitZoom(a+(this._delta>0?h:-h))-a;this._delta=0,this._startTime=null,_&&(i.options.scrollWheelZoom==="center"?i.setZoom(a+_):i.setZoomAround(this._lastMousePos,a+_))}});_t.addInitHook("addHandler","scrollWheelZoom",Di);var ls=600;_t.mergeOptions({tapHold:et.touchNative&&et.safari&&et.mobile,tapTolerance:15});var hl=Xe.extend({addHooks:function(){ut(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){St(this._map._container,"touchstart",this._onDown,this)},_onDown:function(i){if(clearTimeout(this._holdTimeout),i.touches.length===1){var a=i.touches[0];this._startPos=this._newPos=new P(a.clientX,a.clientY),this._holdTimeout=setTimeout(G(function(){this._cancel(),this._isTapValid()&&(ut(document,"touchend",Qt),ut(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",a))},this),ls),ut(document,"touchend touchcancel contextmenu",this._cancel,this),ut(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function i(){St(document,"touchend",Qt),St(document,"touchend touchcancel",i)},_cancel:function(){clearTimeout(this._holdTimeout),St(document,"touchend touchcancel contextmenu",this._cancel,this),St(document,"touchmove",this._onMove,this)},_onMove:function(i){var a=i.touches[0];this._newPos=new P(a.clientX,a.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(i,a){var s=new MouseEvent(i,{bubbles:!0,cancelable:!0,view:window,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY});s._simulated=!0,a.target.dispatchEvent(s)}});_t.addInitHook("addHandler","tapHold",hl),_t.mergeOptions({touchZoom:et.touch,bounceAtZoomLimits:!0});var rn=Xe.extend({addHooks:function(){rt(this._map._container,"leaflet-touch-zoom"),ut(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Rt(this._map._container,"leaflet-touch-zoom"),St(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(i){var a=this._map;if(!(!i.touches||i.touches.length!==2||a._animatingZoom||this._zooming)){var s=a.mouseEventToContainerPoint(i.touches[0]),r=a.mouseEventToContainerPoint(i.touches[1]);this._centerPoint=a.getSize()._divideBy(2),this._startLatLng=a.containerPointToLatLng(this._centerPoint),a.options.touchZoom!=="center"&&(this._pinchStartLatLng=a.containerPointToLatLng(s.add(r)._divideBy(2))),this._startDist=s.distanceTo(r),this._startZoom=a.getZoom(),this._moved=!1,this._zooming=!0,a._stop(),ut(document,"touchmove",this._onTouchMove,this),ut(document,"touchend touchcancel",this._onTouchEnd,this),Qt(i)}},_onTouchMove:function(i){if(!(!i.touches||i.touches.length!==2||!this._zooming)){var a=this._map,s=a.mouseEventToContainerPoint(i.touches[0]),r=a.mouseEventToContainerPoint(i.touches[1]),c=s.distanceTo(r)/this._startDist;if(this._zoom=a.getScaleZoom(c,this._startZoom),!a.options.bounceAtZoomLimits&&(this._zoom<a.getMinZoom()&&c<1||this._zoom>a.getMaxZoom()&&c>1)&&(this._zoom=a._limitZoom(this._zoom)),a.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var h=s._add(r)._divideBy(2)._subtract(this._centerPoint);if(c===1&&h.x===0&&h.y===0)return;this._center=a.unproject(a.project(this._pinchStartLatLng,this._zoom).subtract(h),this._zoom)}this._moved||(a._moveStart(!0,!1),this._moved=!0),Et(this._animRequest);var _=G(a._move,a,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=jt(_,this,!0),Qt(i)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Et(this._animRequest),St(document,"touchmove",this._onTouchMove,this),St(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});_t.addInitHook("addHandler","touchZoom",rn),_t.BoxZoom=fl,_t.DoubleClickZoom=ri,_t.Drag=as,_t.Keyboard=Ra,_t.ScrollWheelZoom=Di,_t.TapHold=hl,_t.TouchZoom=rn,x.Bounds=R,x.Browser=et,x.CRS=kt,x.Canvas=Na,x.Circle=es,x.CircleMarker=Ea,x.Class=de,x.Control=we,x.DivIcon=ns,x.DivOverlay=Je,x.DomEvent=Eu,x.DomUtil=no,x.Draggable=Oi,x.Evented=Q,x.FeatureGroup=Ne,x.GeoJSON=De,x.GridLayer=ln,x.Handler=Xe,x.Icon=Zn,x.ImageOverlay=ui,x.LatLng=I,x.LatLngBounds=k,x.Layer=Qe,x.LayerGroup=nn,x.LineUtil=ho,x.Map=_t,x.Marker=jn,x.Mixin=Nu,x.Path=yi,x.Point=P,x.PolyUtil=Cu,x.Polygon=Hn,x.Polyline=xi,x.Popup=Oa,x.PosAnimation=tl,x.Projection=mo,x.Rectangle=So,x.Renderer=Fe,x.SVG=Ci,x.SVGOverlay=Ni,x.TileLayer=sn,x.Tooltip=cl,x.Transformation=yn,x.Util=qi,x.VideoOverlay=rl,x.bind=G,x.bounds=V,x.canvas=Ca,x.circle=ju,x.circleMarker=po,x.control=Rn,x.divIcon=bo,x.extend=N,x.featureGroup=wa,x.geoJSON=ul,x.geoJson=go,x.gridLayer=We,x.icon=ll,x.imageOverlay=Pn,x.latLng=X,x.latLngBounds=J,x.layerGroup=vo,x.map=Ma,x.marker=ts,x.point=m,x.polygon=Ce,x.polyline=Hu,x.popup=qu,x.rectangle=Ze,x.setOptions=mt,x.stamp=C,x.svg=un,x.svgOverlay=xo,x.tileLayer=Re,x.tooltip=Pu,x.transformation=Pi,x.version=p,x.videoOverlay=yo;var Yn=window.L;x.noConflict=function(){return window.L=Yn,this},window.L=x})}(Ps,Ps.exports)),Ps.exports}var G_=k_();const gn=Ed(G_),Y_=({map:g})=>{const[E,x]=ie.useState(!0),{selectedMapType:p,setSelectedMapType:N,species:Y,toggleSpeciesVisibility:G}=da(),nt=[{id:"terrain",name:"地形图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"streets",name:"街道图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"satellite",name:"卫星图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"},{id:"hybrid",name:"混合图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}],C=[{range:"600+",color:"bg-red-700",count:"600+"},{range:"500-600",color:"bg-red-600",count:"500 - 600"},{range:"400-500",color:"bg-red-500",count:"400 - 500"},{range:"300-400",color:"bg-orange-500",count:"300 - 400"},{range:"250-300",color:"bg-orange-400",count:"250 - 300"},{range:"200-250",color:"bg-yellow-500",count:"200 - 250"},{range:"150-200",color:"bg-yellow-400",count:"150 - 200"},{range:"100-150",color:"bg-yellow-300",count:"100 - 150"},{range:"50-100",color:"bg-green-300",count:"50 - 100"},{range:"15-50",color:"bg-green-200",count:"15 - 50"},{range:"0-15",color:"bg-green-100",count:"0 - 15"}],B=q=>{g&&(N(q.id),g.eachLayer(tt=>{tt instanceof gn.TileLayer&&g.removeLayer(tt)}),gn.tileLayer(q.url,{attribution:q.id==="satellite"||q.id==="hybrid"?"© Esri":"© OpenStreetMap contributors",maxZoom:18}).addTo(g))};return b.jsx("div",{className:"absolute top-4 right-4 z-[1000]",children:b.jsxs("div",{className:"map-controls",children:[b.jsx("button",{onClick:()=>x(!E),className:"flex items-center justify-center w-8 h-8 bg-white rounded-md shadow-md hover:bg-gray-50 mb-2",children:b.jsx(C_,{className:"h-4 w-4 text-gray-600"})}),E&&b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{children:[b.jsx("h6",{className:"text-sm font-semibold text-gray-900 mb-2",children:"地图类型"}),b.jsx("div",{className:"space-y-1",children:nt.map(q=>b.jsxs("label",{className:"flex items-center cursor-pointer",children:[b.jsx("input",{type:"radio",name:"mapType",value:q.id,checked:p===q.id,onChange:()=>B(q),className:"mr-2 text-blue-600"}),b.jsx("span",{className:"text-sm text-gray-700",children:q.name})]},q.id))})]}),b.jsxs("div",{children:[b.jsx("div",{className:"flex items-center mb-2",children:b.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"物种图层"})}),b.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:Y.map(q=>b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-3 h-3 rounded-full mr-2 border border-gray-300",style:{backgroundColor:q.color}}),b.jsx("span",{className:"text-xs text-gray-700",children:q.name})]}),b.jsx("button",{onClick:()=>G(q.id),className:"p-1 hover:bg-gray-100 rounded",children:q.isVisible?b.jsx(Ld,{className:"h-3 w-3 text-gray-600"}):b.jsx(__,{className:"h-3 w-3 text-gray-400"})})]},q.id))})]}),b.jsxs("div",{children:[b.jsxs("div",{className:"flex items-center mb-2",children:[b.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"观察到的鸟种"}),b.jsx("button",{className:"ml-2",children:b.jsx(S_,{className:"h-3 w-3 text-gray-500"})})]}),b.jsx("div",{className:"space-y-1",children:C.map(q=>b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:`w-4 h-3 ${q.color} mr-2 border border-gray-300`}),b.jsx("span",{className:"text-xs text-gray-700",children:q.count})]},q.range))})]})]})]})})};(function(){function g(E){return this instanceof g?(this._canvas=E=typeof E=="string"?document.getElementById(E):E,this._ctx=E.getContext("2d"),this._width=E.width,this._height=E.height,this._max=1,void this.clear()):new g(E)}g.prototype={defaultRadius:25,defaultGradient:{.4:"blue",.6:"cyan",.7:"lime",.8:"yellow",1:"red"},data:function(E,x){return this._data=E,this},max:function(E){return this._max=E,this},add:function(E){return this._data.push(E),this},clear:function(){return this._data=[],this},radius:function(E,x){x=x||15;var p=this._circle=document.createElement("canvas"),N=p.getContext("2d"),Y=this._r=E+x;return p.width=p.height=2*Y,N.shadowOffsetX=N.shadowOffsetY=200,N.shadowBlur=x,N.shadowColor="black",N.beginPath(),N.arc(Y-200,Y-200,E,0,2*Math.PI,!0),N.closePath(),N.fill(),this},gradient:function(E){var x=document.createElement("canvas"),p=x.getContext("2d"),N=p.createLinearGradient(0,0,0,256);x.width=1,x.height=256;for(var Y in E)N.addColorStop(Y,E[Y]);return p.fillStyle=N,p.fillRect(0,0,1,256),this._grad=p.getImageData(0,0,1,256).data,this},draw:function(E){this._circle||this.radius(this.defaultRadius),this._grad||this.gradient(this.defaultGradient);var x=this._ctx;x.clearRect(0,0,this._width,this._height);for(var p,N=0,Y=this._data.length;Y>N;N++)p=this._data[N],x.globalAlpha=Math.max(p[2]/this._max,E||.05),x.drawImage(this._circle,p[0]-this._r,p[1]-this._r);var G=x.getImageData(0,0,this._width,this._height);return this._colorize(G.data,this._grad),x.putImageData(G,0,0),this},_colorize:function(E,x){for(var p,N=3,Y=E.length;Y>N;N+=4)p=4*E[N],p&&(E[N-3]=x[p],E[N-2]=x[p+1],E[N-1]=x[p+2])}},window.simpleheat=g})(),L.HeatLayer=(L.Layer?L.Layer:L.Class).extend({initialize:function(g,E){this._latlngs=g,L.setOptions(this,E)},setLatLngs:function(g){return this._latlngs=g,this.redraw()},addLatLng:function(g){return this._latlngs.push(g),this.redraw()},setOptions:function(g){return L.setOptions(this,g),this._heat&&this._updateOptions(),this.redraw()},redraw:function(){return!this._heat||this._frame||this._map._animating||(this._frame=L.Util.requestAnimFrame(this._redraw,this)),this},onAdd:function(g){this._map=g,this._canvas||this._initCanvas(),g._panes.overlayPane.appendChild(this._canvas),g.on("moveend",this._reset,this),g.options.zoomAnimation&&L.Browser.any3d&&g.on("zoomanim",this._animateZoom,this),this._reset()},onRemove:function(g){g.getPanes().overlayPane.removeChild(this._canvas),g.off("moveend",this._reset,this),g.options.zoomAnimation&&g.off("zoomanim",this._animateZoom,this)},addTo:function(g){return g.addLayer(this),this},_initCanvas:function(){var g=this._canvas=L.DomUtil.create("canvas","leaflet-heatmap-layer leaflet-layer"),E=L.DomUtil.testProp(["transformOrigin","WebkitTransformOrigin","msTransformOrigin"]);g.style[E]="50% 50%";var x=this._map.getSize();g.width=x.x,g.height=x.y;var p=this._map.options.zoomAnimation&&L.Browser.any3d;L.DomUtil.addClass(g,"leaflet-zoom-"+(p?"animated":"hide")),this._heat=simpleheat(g),this._updateOptions()},_updateOptions:function(){this._heat.radius(this.options.radius||this._heat.defaultRadius,this.options.blur),this.options.gradient&&this._heat.gradient(this.options.gradient),this.options.max&&this._heat.max(this.options.max)},_reset:function(){var g=this._map.containerPointToLayerPoint([0,0]);L.DomUtil.setPosition(this._canvas,g);var E=this._map.getSize();this._heat._width!==E.x&&(this._canvas.width=this._heat._width=E.x),this._heat._height!==E.y&&(this._canvas.height=this._heat._height=E.y),this._redraw()},_redraw:function(){var g,E,x,p,N,Y,G,nt,C,B=[],q=this._heat._r,tt=this._map.getSize(),ct=new L.Bounds(L.point([-q,-q]),tt.add([q,q])),Pt=this.options.max===void 0?1:this.options.max,Ut=this.options.maxZoom===void 0?this._map.getMaxZoom():this.options.maxZoom,mt=1/Math.pow(2,Math.max(0,Math.min(Ut-this._map.getZoom(),12))),Xt=q/2,ne=[],Ae=this._map._getMapPanePos(),Mt=Ae.x%Xt,fe=Ae.y%Xt;for(g=0,E=this._latlngs.length;E>g;g++)if(x=this._map.latLngToContainerPoint(this._latlngs[g]),ct.contains(x)){N=Math.floor((x.x-Mt)/Xt)+2,Y=Math.floor((x.y-fe)/Xt)+2;var ht=this._latlngs[g].alt!==void 0?this._latlngs[g].alt:this._latlngs[g][2]!==void 0?+this._latlngs[g][2]:1;C=ht*mt,ne[Y]=ne[Y]||[],p=ne[Y][N],p?(p[0]=(p[0]*p[2]+x.x*C)/(p[2]+C),p[1]=(p[1]*p[2]+x.y*C)/(p[2]+C),p[2]+=C):ne[Y][N]=[x.x,x.y,C]}for(g=0,E=ne.length;E>g;g++)if(ne[g])for(G=0,nt=ne[g].length;nt>G;G++)p=ne[g][G],p&&B.push([Math.round(p[0]),Math.round(p[1]),Math.min(p[2],Pt)]);this._heat.data(B).draw(this.options.minOpacity),this._frame=null},_animateZoom:function(g){var E=this._map.getZoomScale(g.zoom),x=this._map._getCenterOffset(g.center)._multiplyBy(-E).subtract(this._map._getMapPanePos());L.DomUtil.setTransform?L.DomUtil.setTransform(this._canvas,x,E):this._canvas.style[L.DomUtil.TRANSFORM]=L.DomUtil.getTranslateString(x)+" scale("+E+")"}}),L.heatLayer=function(g,E){return new L.HeatLayer(g,E)};const V_=({map:g})=>{const E=ie.useRef({}),{species:x,observations:p}=da();return ie.useEffect(()=>{if(g)return Object.values(E.current).forEach(N=>{g.hasLayer(N)&&g.removeLayer(N)}),E.current={},x.forEach(N=>{if(!N.isVisible)return;const Y=p.filter(nt=>nt.speciesId===N.id);if(Y.length===0)return;const G=gn.layerGroup();Y.forEach(nt=>{const C=gn.circleMarker([nt.lat,nt.lng],{radius:Math.max(3,nt.intensity*15),fillColor:N.color,color:N.color,weight:1,opacity:.8,fillOpacity:Math.max(.3,nt.intensity*.7)});C.bindPopup(`
          <div class="text-sm">
            <strong>${N.name}</strong><br/>
            <em>${N.scientificName}</em><br/>
            观察数量: ${nt.count}
          </div>
        `),G.addLayer(C)}),G.addTo(g),E.current[N.id]=G}),()=>{Object.values(E.current).forEach(N=>{g.hasLayer(N)&&g.removeLayer(N)})}},[g,x,p]),null},X_=()=>{const[g,E]=ie.useState(!1),[x,p]=ie.useState(""),{species:N,toggleSpeciesVisibility:Y}=da(),G=N.filter(C=>C.name.toLowerCase().includes(x.toLowerCase())||C.scientificName.toLowerCase().includes(x.toLowerCase())),nt=N.filter(C=>C.isVisible).length;return b.jsx("div",{className:"absolute top-20 left-4 z-[1000] max-w-xs sm:max-w-sm",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200",children:[b.jsxs("button",{onClick:()=>E(!g),className:"flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 rounded-lg",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx(g_,{className:"h-4 w-4 text-gray-600 mr-2"}),b.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["物种筛选 (",nt,"/",N.length,")"]})]}),b.jsx(Od,{className:`h-4 w-4 text-gray-400 transition-transform ${g?"rotate-45":""}`})]}),g&&b.jsxs("div",{className:"border-t border-gray-200 p-4 w-full sm:w-80",children:[b.jsxs("div",{className:"relative mb-4",children:[b.jsx(xc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),b.jsx("input",{type:"text",placeholder:"搜索物种...",value:x,onChange:C=>p(C.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),b.jsx("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:G.map(C=>b.jsxs("div",{className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md cursor-pointer",onClick:()=>Y(C.id),children:[b.jsxs("div",{className:"flex items-center flex-1",children:[b.jsx("div",{className:"w-4 h-4 rounded-full mr-3 border-2 border-gray-300 flex-shrink-0",style:{backgroundColor:C.isVisible?C.color:"transparent"}}),b.jsxs("div",{className:"flex-1 min-w-0",children:[b.jsx("div",{className:"text-sm font-medium text-gray-900 truncate",children:C.name}),b.jsx("div",{className:"text-xs text-gray-500 truncate",children:C.scientificName})]})]}),b.jsx("input",{type:"checkbox",checked:C.isVisible,onChange:()=>Y(C.id),className:"ml-2 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",onClick:B=>B.stopPropagation()})]},C.id))}),G.length===0&&b.jsx("div",{className:"text-center py-4 text-gray-500 text-sm",children:"未找到匹配的物种"}),b.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:b.jsxs("div",{className:"flex space-x-2",children:[b.jsx("button",{onClick:()=>{N.forEach(C=>{C.isVisible||Y(C.id)})},className:"flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200",children:"全选"}),b.jsx("button",{onClick:()=>{N.forEach(C=>{C.isVisible&&Y(C.id)})},className:"flex-1 px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:"全不选"})]})})]})]})})},Q_=()=>{const{species:g,observations:E}=da(),x=g.filter(nt=>nt.isVisible),p=E.filter(nt=>x.some(C=>C.id===nt.speciesId)),N=p.length,Y=p.reduce((nt,C)=>nt+C.count,0),G=N>0?Math.round(Y/N):0;return b.jsx("div",{className:"absolute bottom-4 left-4 z-[1000] max-w-xs",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200 p-3 sm:p-4",children:[b.jsxs("div",{className:"flex items-center mb-3",children:[b.jsx(f_,{className:"h-4 w-4 text-blue-600 mr-2"}),b.jsx("h3",{className:"text-sm font-semibold text-gray-900",children:"观察统计"})]}),b.jsxs("div",{className:"space-y-2 text-sm",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx(Ld,{className:"h-3 w-3 text-gray-500 mr-2"}),b.jsx("span",{className:"text-gray-700",children:"显示物种:"})]}),b.jsxs("span",{className:"font-medium text-gray-900",children:[x.length,"/",g.length]})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx(Ad,{className:"h-3 w-3 text-gray-500 mr-2"}),b.jsx("span",{className:"text-gray-700",children:"观察点:"})]}),b.jsx("span",{className:"font-medium text-gray-900",children:N.toLocaleString()})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsx("span",{className:"text-gray-700",children:"总观察数:"}),b.jsx("span",{className:"font-medium text-gray-900",children:Y.toLocaleString()})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsx("span",{className:"text-gray-700",children:"平均数量:"}),b.jsx("span",{className:"font-medium text-gray-900",children:G})]})]}),x.length>0&&b.jsxs("div",{className:"mt-4 pt-3 border-t border-gray-200",children:[b.jsx("div",{className:"text-xs text-gray-600 mb-2",children:"物种分布:"}),b.jsx("div",{className:"space-y-1",children:x.map(nt=>{const B=E.filter(q=>q.speciesId===nt.id).reduce((q,tt)=>q+tt.count,0);return b.jsxs("div",{className:"flex items-center justify-between text-xs",children:[b.jsxs("div",{className:"flex items-center",children:[b.jsx("div",{className:"w-2 h-2 rounded-full mr-2",style:{backgroundColor:nt.color}}),b.jsx("span",{className:"text-gray-700 truncate max-w-20",children:nt.name})]}),b.jsx("span",{className:"text-gray-900 font-medium",children:B.toLocaleString()})]},nt.id)})})]})]})})},K_=({message:g="加载中..."})=>b.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-[2000]",children:b.jsxs("div",{className:"text-center",children:[b.jsx(E_,{className:"h-8 w-8 animate-spin text-blue-600 mx-auto mb-2"}),b.jsx("p",{className:"text-sm text-gray-600",children:g})]})}),J_=()=>{const[g,E]=ie.useState(!1),x=[{key:"1-5",description:"切换对应物种的显示/隐藏"},{key:"Ctrl+A",description:"全选/全不选物种"},{key:"Esc",description:"关闭面板"}];return b.jsxs(b.Fragment,{children:[b.jsx("button",{onClick:()=>E(!0),className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-[1000] bg-white hover:bg-gray-50 rounded-full p-2 shadow-lg border border-gray-200",title:"帮助和快捷键",children:b.jsx(d_,{className:"h-5 w-5 text-gray-600"})}),g&&b.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[3000] p-4",children:b.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto",children:[b.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[b.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"帮助和快捷键"}),b.jsx("button",{onClick:()=>E(!1),className:"p-1 hover:bg-gray-100 rounded",children:b.jsx(Od,{className:"h-5 w-5 text-gray-500"})})]}),b.jsxs("div",{className:"p-4 space-y-6",children:[b.jsxs("div",{children:[b.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"关于此应用"}),b.jsx("p",{className:"text-sm text-gray-600",children:"这是一个基于 eBird 的观鸟热点地图应用，展示了不同物种在各地区的分布情况。 您可以通过筛选和搜索功能来探索不同物种的观察数据。"})]}),b.jsxs("div",{children:[b.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"主要功能"}),b.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[b.jsx("li",{children:"• 交互式地图显示物种分布"}),b.jsx("li",{children:"• 多物种图层叠加"}),b.jsx("li",{children:"• 物种筛选和搜索"}),b.jsx("li",{children:"• 地图类型切换"}),b.jsx("li",{children:"• 实时统计信息"})]})]}),b.jsxs("div",{children:[b.jsxs("div",{className:"flex items-center mb-2",children:[b.jsx(M_,{className:"h-4 w-4 text-gray-600 mr-2"}),b.jsx("h3",{className:"text-md font-medium text-gray-900",children:"键盘快捷键"})]}),b.jsx("div",{className:"space-y-2",children:x.map((p,N)=>b.jsxs("div",{className:"flex items-center justify-between text-sm",children:[b.jsx("span",{className:"font-mono bg-gray-100 px-2 py-1 rounded text-gray-800",children:p.key}),b.jsx("span",{className:"text-gray-600 flex-1 ml-3",children:p.description})]},N))})]}),b.jsxs("div",{children:[b.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"地图操作"}),b.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[b.jsx("li",{children:"• 拖拽移动地图"}),b.jsx("li",{children:"• 滚轮缩放"}),b.jsx("li",{children:"• 点击标记查看详情"}),b.jsx("li",{children:"• 使用右侧控制面板切换图层"})]})]})]}),b.jsx("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:b.jsx("p",{className:"text-xs text-gray-500 text-center",children:"基于 React + Leaflet 构建 | 数据为模拟数据"})})]})})]})};delete gn.Icon.Default.prototype._getIconUrl;gn.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const W_=()=>{const g=ie.useRef(null),E=ie.useRef(null),[x,p]=ie.useState(!0),{mapCenter:N,mapZoom:Y,setMapCenter:G,setMapZoom:nt}=da();return ie.useEffect(()=>{if(!g.current||E.current)return;const C=gn.map(g.current,{center:N,zoom:Y,zoomControl:!1}),B=gn.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"© OpenStreetMap contributors",maxZoom:18});return B.on("load",()=>{p(!1)}),B.addTo(C),gn.control.zoom({position:"bottomright"}).addTo(C),C.on("moveend",()=>{const q=C.getCenter();G([q.lat,q.lng])}),C.on("zoomend",()=>{nt(C.getZoom())}),E.current=C,()=>{E.current&&(E.current.remove(),E.current=null)}},[G,nt]),b.jsxs("div",{className:"relative w-full h-full",children:[b.jsx("div",{ref:g,className:"w-full h-full"}),x&&b.jsx(K_,{message:"正在加载地图..."}),b.jsx(V_,{map:E.current}),b.jsx(X_,{}),b.jsx(Q_,{}),b.jsx(J_,{}),b.jsx(Y_,{map:E.current})]})},F_=()=>{const{species:g,toggleSpeciesVisibility:E}=da();ie.useEffect(()=>{const x=p=>{if(!(p.target instanceof HTMLInputElement||p.target instanceof HTMLTextAreaElement))switch(p.key.toLowerCase()){case"1":case"2":case"3":case"4":case"5":p.preventDefault();const N=parseInt(p.key)-1;g[N]&&E(g[N].id);break;case"a":if(p.ctrlKey||p.metaKey){p.preventDefault();const Y=g.every(G=>G.isVisible);g.forEach(G=>{(Y&&G.isVisible||!Y&&!G.isVisible)&&E(G.id)})}break;case"escape":p.preventDefault();break}};return document.addEventListener("keydown",x),()=>{document.removeEventListener("keydown",x)}},[g,E])};function I_(){const g=da(E=>E.initializeData);return F_(),ie.useEffect(()=>{g()},[g]),b.jsxs("div",{className:"min-h-screen bg-gray-50",children:[b.jsx(R_,{}),b.jsxs("main",{className:"flex flex-col h-screen",children:[b.jsx(q_,{}),b.jsx("div",{className:"flex-1 relative",children:b.jsx(W_,{})})]})]})}t_.createRoot(document.getElementById("root")).render(b.jsx(ie.StrictMode,{children:b.jsx(I_,{})}));
