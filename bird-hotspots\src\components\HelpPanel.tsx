import { useState } from 'react';
import { HelpCircle, X, Keyboard } from 'lucide-react';

const HelpPanel = () => {
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    { key: '1-5', description: '切换对应物种的显示/隐藏' },
    { key: 'Ctrl+A', description: '全选/全不选物种' },
    { key: 'Esc', description: '关闭面板' },
  ];

  return (
    <>
      {/* Help Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="absolute top-4 left-1/2 transform -translate-x-1/2 z-[1000] bg-white hover:bg-gray-50 rounded-full p-2 shadow-lg border border-gray-200"
        title="帮助和快捷键"
      >
        <HelpCircle className="h-5 w-5 text-gray-600" />
      </button>

      {/* Help Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[3000] p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">帮助和快捷键</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4 space-y-6">
              {/* About */}
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">关于此应用</h3>
                <p className="text-sm text-gray-600">
                  这是一个基于 eBird 的观鸟热点地图应用，展示了不同物种在各地区的分布情况。
                  您可以通过筛选和搜索功能来探索不同物种的观察数据。
                </p>
              </div>

              {/* Features */}
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">主要功能</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 交互式地图显示物种分布</li>
                  <li>• 多物种图层叠加</li>
                  <li>• 物种筛选和搜索</li>
                  <li>• 地图类型切换</li>
                  <li>• 实时统计信息</li>
                </ul>
              </div>

              {/* Keyboard Shortcuts */}
              <div>
                <div className="flex items-center mb-2">
                  <Keyboard className="h-4 w-4 text-gray-600 mr-2" />
                  <h3 className="text-md font-medium text-gray-900">键盘快捷键</h3>
                </div>
                <div className="space-y-2">
                  {shortcuts.map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-gray-800">
                        {shortcut.key}
                      </span>
                      <span className="text-gray-600 flex-1 ml-3">
                        {shortcut.description}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Map Controls */}
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">地图操作</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 拖拽移动地图</li>
                  <li>• 滚轮缩放</li>
                  <li>• 点击标记查看详情</li>
                  <li>• 使用右侧控制面板切换图层</li>
                </ul>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <p className="text-xs text-gray-500 text-center">
                基于 React + Leaflet 构建 | 数据为模拟数据
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default HelpPanel;
