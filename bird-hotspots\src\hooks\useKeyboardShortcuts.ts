import { useEffect } from 'react';
import { useAppStore } from '../store/useAppStore';

export const useKeyboardShortcuts = () => {
  const { species, toggleSpeciesVisibility } = useAppStore();

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
          event.preventDefault();
          const index = parseInt(event.key) - 1;
          if (species[index]) {
            toggleSpeciesVisibility(species[index].id);
          }
          break;
        case 'a':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            // Toggle all species
            const allVisible = species.every(s => s.isVisible);
            species.forEach(s => {
              if (allVisible && s.isVisible) {
                toggleSpeciesVisibility(s.id);
              } else if (!allVisible && !s.isVisible) {
                toggleSpeciesVisibility(s.id);
              }
            });
          }
          break;
        case 'escape':
          // Close any open panels (could be extended)
          event.preventDefault();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [species, toggleSpeciesVisibility]);
};
