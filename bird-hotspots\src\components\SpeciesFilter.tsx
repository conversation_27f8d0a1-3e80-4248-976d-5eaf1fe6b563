import { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

const SpeciesFilter = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { species, toggleSpeciesVisibility } = useAppStore();

  const filteredSpecies = species.filter(s => 
    s.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    s.scientificName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const visibleCount = species.filter(s => s.isVisible).length;

  return (
    <div className="absolute top-20 left-4 z-[1000] max-w-xs sm:max-w-sm">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200">
        {/* Toggle Button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 rounded-lg"
        >
          <div className="flex items-center">
            <Filter className="h-4 w-4 text-gray-600 mr-2" />
            <span className="text-sm font-medium text-gray-900">
              物种筛选 ({visibleCount}/{species.length})
            </span>
          </div>
          <X className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-45' : ''}`} />
        </button>

        {isOpen && (
          <div className="border-t border-gray-200 p-4 w-full sm:w-80">
            {/* Search Input */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索物种..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>

            {/* Species List */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredSpecies.map((speciesItem) => (
                <div
                  key={speciesItem.id}
                  className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                  onClick={() => toggleSpeciesVisibility(speciesItem.id)}
                >
                  <div className="flex items-center flex-1">
                    <div
                      className="w-4 h-4 rounded-full mr-3 border-2 border-gray-300 flex-shrink-0"
                      style={{ backgroundColor: speciesItem.isVisible ? speciesItem.color : 'transparent' }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {speciesItem.name}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {speciesItem.scientificName}
                      </div>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    checked={speciesItem.isVisible}
                    onChange={() => toggleSpeciesVisibility(speciesItem.id)}
                    className="ml-2 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              ))}
            </div>

            {filteredSpecies.length === 0 && (
              <div className="text-center py-4 text-gray-500 text-sm">
                未找到匹配的物种
              </div>
            )}

            {/* Quick Actions */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    species.forEach(s => {
                      if (!s.isVisible) toggleSpeciesVisibility(s.id);
                    });
                  }}
                  className="flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                >
                  全选
                </button>
                <button
                  onClick={() => {
                    species.forEach(s => {
                      if (s.isVisible) toggleSpeciesVisibility(s.id);
                    });
                  }}
                  className="flex-1 px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  全不选
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SpeciesFilter;
