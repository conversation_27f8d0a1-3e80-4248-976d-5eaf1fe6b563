import { useEffect, useRef } from 'react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';

// Import heat layer plugin
import 'leaflet.heat';

interface HeatmapLayerProps {
  map: L.Map | null;
}

const HeatmapLayer = ({ map }: HeatmapLayerProps) => {
  const layerGroupsRef = useRef<{ [key: string]: L.LayerGroup }>({});
  const { species, observations } = useAppStore();

  useEffect(() => {
    if (!map) return;

    // Clear existing layer groups
    Object.values(layerGroupsRef.current).forEach(layerGroup => {
      if (map.hasLayer(layerGroup)) {
        map.removeLayer(layerGroup);
      }
    });
    layerGroupsRef.current = {};

    // Create layer groups for each visible species
    species.forEach(speciesItem => {
      if (!speciesItem.isVisible) return;

      // Filter observations for this species
      const speciesObservations = observations.filter(
        obs => obs.speciesId === speciesItem.id
      );

      if (speciesObservations.length === 0) return;

      // Create layer group for this species
      const layerGroup = L.layerGroup();

      // Add circle markers for each observation
      speciesObservations.forEach(obs => {
        const circle = L.circleMarker([obs.lat, obs.lng], {
          radius: Math.max(3, obs.intensity * 15),
          fillColor: speciesItem.color,
          color: speciesItem.color,
          weight: 1,
          opacity: 0.8,
          fillOpacity: Math.max(0.3, obs.intensity * 0.7)
        });

        // Add popup with observation details
        circle.bindPopup(`
          <div class="text-sm">
            <strong>${speciesItem.name}</strong><br/>
            <em>${speciesItem.scientificName}</em><br/>
            观察数量: ${obs.count}
          </div>
        `);

        layerGroup.addLayer(circle);
      });

      // Add to map and store reference
      layerGroup.addTo(map);
      layerGroupsRef.current[speciesItem.id] = layerGroup;
    });

    // Cleanup function
    return () => {
      Object.values(layerGroupsRef.current).forEach(layerGroup => {
        if (map.hasLayer(layerGroup)) {
          map.removeLayer(layerGroup);
        }
      });
    };
  }, [map, species, observations]);

  return null; // This component doesn't render anything
};

export default HeatmapLayer;
